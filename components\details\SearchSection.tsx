"use client"

import { <PERSON>, RefreshCw, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useLanguage } from "@/contexts/language-context"

interface SearchSectionProps {
  searchByName: string
  searchByMontant: string
  isSearching: boolean
  onSearchByNameChange: (value: string) => void
  onSearchByMontantChange: (value: string) => void
  onSearch: () => void
  onClearSearch: () => void
}

export function SearchSection({
  searchByName,
  searchByMontant,
  isSearching,
  onSearchByNameChange,
  onSearchByMontantChange,
  onSearch,
  onClearSearch
}: SearchSectionProps) {
  const { isRTL } = useLanguage()

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Search by Name */}
        <div className="relative">
          <div
            className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} bg-gray-50 rounded-lg px-4 py-3 border`}
          >
            <Search className={`h-5 w-5 text-gray-400 ${isRTL ? "ml-3" : "mr-3"}`} />
            <Input
              type="text"
              placeholder="Search by name..."
              value={searchByName}
              onChange={(e) => onSearchByNameChange(e.target.value)}
              className={`border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 p-0 ${isRTL ? "text-right" : "text-left"}`}
              dir={isRTL ? "rtl" : "ltr"}
            />
          </div>
        </div>

        {/* Search by Montant */}
        <div className="relative">
          <div
            className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} bg-gray-50 rounded-lg px-4 py-3 border`}
          >
            <Search className={`h-5 w-5 text-gray-400 ${isRTL ? "ml-3" : "mr-3"}`} />
            <Input
              type="number"
              placeholder="Search by amount..."
              value={searchByMontant}
              onChange={(e) => onSearchByMontantChange(e.target.value)}
              className={`border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 p-0 ${isRTL ? "text-right" : "text-left"}`}
            />
          </div>
        </div>
      </div>

      {/* Search Actions */}
      <div className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
        <Button
          onClick={onSearch}
          disabled={isSearching || (!searchByName.trim() && !searchByMontant.trim())}
          className="bg-[#0A66C2] hover:bg-[#004182] text-white"
        >
          {isSearching ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Search className="h-4 w-4 mr-2" />
          )}
          Search
        </Button>
        
        {(searchByName.trim() || searchByMontant.trim()) && (
          <Button
            variant="outline"
            onClick={onClearSearch}
            className="border-gray-300"
          >
            <X className="h-4 w-4 mr-2" />
            Clear
          </Button>
        )}
      </div>
    </div>
  )
}
