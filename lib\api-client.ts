import { apiClient } from "./api/client"
import type { 
  TablesResponse, 
  TableResponse,
  UpdateTableRequest,
  RecordsResponse,
  RecordResponse,
  CreateRecordRequest,
  UpdateRecordRequest
} from "./api-client-types"

export class ApiClient {
  // Tables API
  static async getTables(): Promise<TablesResponse> {

    try {
      const response = await apiClient.get<any>("/coffre-fort")

      // Map API response fields to frontend interface
      const mappedResponse = Array.isArray(response) ? response.map((table: any) => ({
        ...table,
        coffreColor: table.coffreColor // Map colorHex to coffreColor
      })) : response

      return mappedResponse
    } catch (error) {
      throw error
    }
  }

  static async updateTable(id: number, data: UpdateTableRequest): Promise<TableResponse> {
    const response = await apiClient.put<TableResponse>(`/coffre-fort/${id}`, data)
    return response
  }
  // Records API (table-specific) - Enhanced with mock/real API handling
  static async getRecords(tableName: string, params?: { page?: number; size?: number }): Promise<RecordsResponse> {
    try {
      const response = await apiClient.get<RecordsResponse>(`/${tableName}`, params)

      // In mock mode, the response is direct data
      // In real mode, the response should have a 'data' property
      const isMockMode = process.env.NEXT_PUBLIC_USE_MOCK_API === "true"
      if (isMockMode) {
        // Mock API returns data directly
        return response as unknown as RecordsResponse
      } else {
        // Real API returns { data: RecordsResponse }
        return response
      }
    } catch (error) {
      throw error
    }
  }

  static async createRecord(tableName: string, data: CreateRecordRequest): Promise<RecordResponse> {
    const response = await apiClient.post<RecordResponse>(`/${tableName}`, data)
    return response
  }

  static async updateRecord(tableName: string, id: number, data: UpdateRecordRequest): Promise<RecordResponse> {
    const response = await apiClient.put<RecordResponse>(`/${tableName}/${id}`, data)
    return response
  }

  static async deleteRecord(tableName: string, id: number): Promise<void> {
    await apiClient.delete<void>(`/${tableName}/${id}`)
  }
}
