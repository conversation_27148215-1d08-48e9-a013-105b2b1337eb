if(!self.define){let e,s={};const a=(a,i)=>(a=new URL(a+".js",i).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(i,n)=>{const c=e||("document"in self?document.currentScript.src:"")||location.href;if(s[c])return;let t={};const r=e=>a(e,c),o={module:{uri:c},exports:t,require:r};s[c]=Promise.all(i.map(e=>o[e]||r(e))).then(e=>(n(...e),t))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"41cf744591053184dd5d587e546c96fa"},{url:"/_next/static/I0I_9xSjMamCE4-4j4ZXs/_buildManifest.js",revision:"172e769da91baa11de9b258fb2d92f86"},{url:"/_next/static/I0I_9xSjMamCE4-4j4ZXs/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/117-a6a34b5e8897055c.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/164f4fb6-89ef436be78edcb9.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/235-c4580b98cc86ed44.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/241-a9731a990b616358.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/267-3ad2c230d76a3e2b.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/299-0a5c3fbc8dc4f4ed.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/331-fba89e6798b908ed.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/501-4bb73ae7b868ef96.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/561-02b3c83853c13eba.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/648-014eb6d907bff49c.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/65-c9bfbf3c7be024b0.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/694-105e366e3ae7bd91.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/770-4fa9b0351cf8aac6.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/848.7ae3886ad9fcb532.js",revision:"7ae3886ad9fcb532"},{url:"/_next/static/chunks/849-344a2e9b71aa0a5b.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/969.615b5b52f2ffa4a7.js",revision:"615b5b52f2ffa4a7"},{url:"/_next/static/chunks/ad2866b8-1b5280da7a09469f.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/_not-found/page-d9a04ac169f23d37.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/details/%5Bid%5D/loading-102bf6d6bd6a43ab.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/details/%5Bid%5D/page-925ff419e812aeae.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/layout-5ccfc75fd13c2d72.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/loading-06608d0ff19314b3.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/page-6efd7dfe9b78e00a.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/profile/page-bbc32e6a2f7c721c.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/search/loading-48dc64830f333eb7.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/search/page-a31be127cb6ef0a1.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/app/signin/page-1744bab4a87ac33e.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/bc98253f.2a86d0955713b458.js",revision:"2a86d0955713b458"},{url:"/_next/static/chunks/fd9d1056-5964dcb94ad8067e.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/framework-00a8ba1a63cfdc9e.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/main-app-35cc2f52fbfbfcae.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/main-d22a87d922c8efcf.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/pages/_app-15e2daefa259f0b5.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/pages/_error-28b803cb2479b966.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-a889da25ca0325d6.js",revision:"I0I_9xSjMamCE4-4j4ZXs"},{url:"/_next/static/css/238ef09d5f5c1343.css",revision:"238ef09d5f5c1343"},{url:"/_next/static/media/26a46d62cd723877-s.woff2",revision:"befd9c0fdfa3d8a645d5f95717ed6420"},{url:"/_next/static/media/55c55f0601d81cf3-s.woff2",revision:"43828e14271c77b87e3ed582dbff9f74"},{url:"/_next/static/media/581909926a08bbc8-s.woff2",revision:"f0b86e7c24f455280b8df606b89af891"},{url:"/_next/static/media/8e9860b6e62d6359-s.woff2",revision:"01ba6c2a184b8cba08b0d57167664d75"},{url:"/_next/static/media/97e0cb1ae144a2a9-s.woff2",revision:"e360c61c5bd8d90639fd4503c829c2dc"},{url:"/_next/static/media/df0a9ae256c0569c-s.woff2",revision:"d54db44de5ccb18886ece2fda72bdfe0"},{url:"/_next/static/media/e4af272ccee01ff0-s.p.woff2",revision:"65850a373e258f1c897a2b3d75eb74de"},{url:"/app_logo.png",revision:"52f20ebbab802d7f01a28f91b2510300"},{url:"/apple-touch-icon.png",revision:"4c992d399794b104e9daa15dc117d36b"},{url:"/favicon.ico",revision:"5d38354de9421e51bf1a93e89c0aced2"},{url:"/firebase-messaging-sw.js",revision:"86232a44263e7d483d3ca38337f3c722"},{url:"/icons.json",revision:"88de5fe67e0b66860aa3f4ebe8bae889"},{url:"/ios/100.png",revision:"7e9cac6a7d35b7d5ef099bee0b103c03"},{url:"/ios/1024.png",revision:"6b3649eda3aa3ce3ab2dfa4608c7a839"},{url:"/ios/114.png",revision:"4b77610e7f018c940bbe7d4d277203f5"},{url:"/ios/120.png",revision:"c7ba355122ba7a627f37371d79f52baf"},{url:"/ios/128.png",revision:"2d618d90be2d5d526aaca78552ae4705"},{url:"/ios/144.png",revision:"1d2e84b31836f29bb8884cd6a78f0877"},{url:"/ios/152.png",revision:"2f67559c51410f8606cfc2d9759aaacf"},{url:"/ios/16.png",revision:"712d6926267fffe61f24f4e33ad60b84"},{url:"/ios/167.png",revision:"57401248e26631e092cce0f88d838807"},{url:"/ios/180.png",revision:"8a1cd2bcdfe6b4b56a7fbccb4f729b4d"},{url:"/ios/192.png",revision:"291457435108937c2144244ac66240aa"},{url:"/ios/20.png",revision:"9160a797caafd1ac6f7de119b7c8828f"},{url:"/ios/256.png",revision:"f49d5082b7477e2c97c3ac6b38a61e27"},{url:"/ios/29.png",revision:"dba9f9ccee8e3a2647ea5e53b56f5fb4"},{url:"/ios/32.png",revision:"090e7efa058adedfbbd27cc869528e8c"},{url:"/ios/40.png",revision:"afae33bf265f3b7e12b77b68481a656a"},{url:"/ios/50.png",revision:"94a9c5e0a5ff82e110f2ee2bb5017e60"},{url:"/ios/512.png",revision:"fb97a5646496d290bee7ccdae341201b"},{url:"/ios/57.png",revision:"8d6f4b319ef08b826b3c94700640b090"},{url:"/ios/58.png",revision:"25405ffe36b3155a90f925f0c8f2c13a"},{url:"/ios/60.png",revision:"3c2e87edd24e1e1f8e7d65fc60205d10"},{url:"/ios/64.png",revision:"c082879ebe273cf0e50ec754e79c59ff"},{url:"/ios/72.png",revision:"f01ee30709bc23cb5cdb6c60cd41a7f7"},{url:"/ios/76.png",revision:"e6eb1183006d332c6c4ceee2d1721925"},{url:"/ios/80.png",revision:"4328dc322ff526f2f707a4bf8710000c"},{url:"/ios/87.png",revision:"4ae9f2f8b62d32cf49a881c8e2d667ca"},{url:"/manifest.json",revision:"6d67b94d30ddaa834fb0e434ac7cc002"},{url:"/placeholder-logo.png",revision:"95d8d1a4a9bbcccc875e2c381e74064a"},{url:"/placeholder-logo.svg",revision:"1e16dc7df824652c5906a2ab44aef78c"},{url:"/placeholder-user.jpg",revision:"7ee6562646feae6d6d77e2c72e204591"},{url:"/placeholder.jpg",revision:"1e533b7b4545d1d605144ce893afc601"},{url:"/placeholder.svg",revision:"35707bd9960ba5281c72af927b79291f"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:i})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
