"use client"

import { User, LogOut, <PERSON>ader2, <PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useLanguage } from "@/contexts/language-context"
import { useAuth } from "@/contexts/auth-context"
import { useDirection } from "@radix-ui/react-direction"
import { useEnhancedAnalytics } from "@/hooks/useAnalytics"
import { useState } from "react"
import Link from "next/link"
import { ChangePasswordDialog } from "./change-password-dialog"

export function UserMenu() {
  const { t } = useLanguage()
  const { signOut, isLoading } = useAuth()
  const analytics = useEnhancedAnalytics()
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [showChangePassword, setShowChangePassword] = useState(false)
  const dir = useDirection()

  // Static user information
  const user = {
    name: "Adil",
    email: "<EMAIL>",
    avatar: "/placeholder.svg"
  }

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)

      // Track sign out attempt from user menu
      analytics.trackButtonClick('sign_out', 'user_menu')

      await signOut()
    } catch (error) {
      console.error("Sign out error:", error)
    } finally {
      setIsSigningOut(false)
    }
  }

  return (
    <>
      <DropdownMenu onOpenChange={(open) => {
        if (open) {
          analytics.trackMenuOpen('user_menu', 'header')
        }
      }}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full" disabled={isLoading || isSigningOut}>
          <Avatar className="h-10 w-10">
            <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
            <AvatarFallback className="bg-[#0A66C2] text-white">{user.name.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
          {(isLoading || isSigningOut) && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-full">
              <Loader2 className="h-4 w-4 animate-spin text-white" />
            </div>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align={dir === "rtl" ? "start" : "end"} forceMount>
        <div className="flex items-center justify-start space-x-2 rtl:space-x-reverse rtl:flex-row-reverse p-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
            <AvatarFallback className="bg-[#0A66C2] text-white text-sm">
              {user.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col space-y-1 leading-none">
            <p className="font-medium">{user.name}</p>
            <p className="w-[200px] truncate text-sm text-muted-foreground">{user.email}</p>
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer"
          onClick={() => {
            analytics.trackDialogInteraction('change_password', 'open', 'user_menu')
            setShowChangePassword(true)
          }}
        >
          <Lock className="mr-2 h-4 w-4 rtl:mr-0 rtl:ml-2" />
          <span>Changer le mot de passe</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="cursor-pointer text-red-600 focus:text-red-600"
          onClick={handleSignOut}
          disabled={isSigningOut}
        >
          {isSigningOut ? (
            <Loader2 className="mr-2 h-4 w-4 rtl:mr-0 rtl:ml-2 animate-spin" />
          ) : (
            <LogOut className="mr-2 h-4 w-4 rtl:mr-0 rtl:ml-2" />
          )}
          <span>{isSigningOut ? "Signing out..." : "Sign out"}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

      <ChangePasswordDialog
        open={showChangePassword}
        onOpenChange={setShowChangePassword}
      />
    </>
  )
}
