"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { useLogin, useLogout } from "@/lib/api/services/auth"
import { tokenStorage, setupTokenCleanup } from "@/lib/utils/storage"

interface AuthContextType {
  isLoading: boolean
  isAuthenticated: boolean
  signIn: (email: string, password: string) => Promise<boolean>
  signOut: () => Promise<void>
  error: string | null
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [error, setError] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)

  // Set client flag and setup token cleanup on browser close
  useEffect(() => {
    setIsClient(true)

    // Setup token cleanup when browser/app is closed
    const cleanup = setupTokenCleanup()

    return cleanup
  }, [])

  // Use React Query hooks for auth operations
  const loginMutation = useLogin()
  const logoutMutation = useLogout()

  const isAuthenticated = isClient && tokenStorage.hasValidTokens()
  const isLoading = !isClient || loginMutation.isPending || logoutMutation.isPending

  const signIn = async (email: string, password: string): Promise<boolean> => {
    try {
      setError(null)
      console.log("Starting JWT sign in process...")

      await loginMutation.mutateAsync({ username: email, password })
      console.log("Sign in successful, tokens stored:", !!tokenStorage.hasValidTokens())

      return true
    } catch (err: any) {
      console.error("Sign in failed:", err)
      setError(err.message || "Login failed")
      return false
    }
  }

  const signOut = async (): Promise<void> => {
    try {
      console.log("Starting sign out process...")

      // Clear JWT tokens immediately
      tokenStorage.clearTokens()

      // Call the logout mutation to clear cache
      logoutMutation.mutate(undefined, {
        onSettled: () => {
          console.log("Sign out completed, redirecting to signin...")
          // Force redirect to signin page
          if (typeof window !== "undefined") {
            window.location.href = "/signin"
          }
        },
      })
    } catch (err: any) {
      console.error("Sign out error:", err)
      // Even if logout fails, clear local data and redirect
      tokenStorage.clearTokens()
      if (typeof window !== "undefined") {
        window.location.href = "/signin"
      }
    }
  }

  const clearError = () => {
    setError(null)
  }

  return (
    <AuthContext.Provider
      value={{
        isLoading,
        isAuthenticated,
        signIn,
        signOut,
        error,
        clearError,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
