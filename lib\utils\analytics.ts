/**
 * Analytics Utilities
 * Centralized analytics functions for Google Analytics
 */

import { sendGAEvent } from "@next/third-parties/google"

// Analytics Event Types
export interface AnalyticsEvent {
  action: string
  category: string
  label?: string
  value?: number
  custom_parameters?: Record<string, any>
}

// Common Event Categories
export const ANALYTICS_CATEGORIES = {
  AUTHENTICATION: 'authentication',
  NAVIGATION: 'navigation',
  USER_INTERACTION: 'user_interaction',
  CRUD_OPERATIONS: 'crud_operations',
  PDF_GENERATION: 'pdf_generation',
  ERROR: 'error',
  PERFORMANCE: 'performance',
  UI_INTERACTION: 'ui_interaction'
} as const

// Common Event Actions
export const ANALYTICS_ACTIONS = {
  // Authentication
  SIGN_IN_ATTEMPT: 'sign_in_attempt',
  SIGN_IN_SUCCESS: 'sign_in_success',
  SIGN_IN_FAILURE: 'sign_in_failure',
  SIGN_OUT: 'sign_out',
  SESSION_TIMEOUT: 'session_timeout',
  
  NAVIGATE_TO_DETAILS: 'navigate_to_details',
  NAVIGATE_TO_HOME: 'navigate_to_home',
  BACK_NAVIGATION: 'back_navigation',
  
  // CRUD Operations
  CREATE_RECORD: 'create_record',
  UPDATE_RECORD: 'update_record',
  DELETE_RECORD: 'delete_record',
  VIEW_RECORD: 'view_record',
  SEARCH_RECORDS: 'search_records',
  
  // Table Operations
  VIEW_TABLE: 'view_table',
  UPDATE_TABLE: 'update_table',
  REFRESH_TABLE: 'refresh_table',
  
  // PDF Generation
  PDF_DOWNLOAD_ATTEMPT: 'pdf_download_attempt',
  PDF_DOWNLOAD_SUCCESS: 'pdf_download_success',
  PDF_DOWNLOAD_FAILURE: 'pdf_download_failure',
  
  // UI Interactions
  BUTTON_CLICK: 'button_click',
  TOGGLE_VISIBILITY: 'toggle_visibility',
  LANGUAGE_CHANGE: 'language_change',
  MENU_OPEN: 'menu_open',
  DIALOG_OPEN: 'dialog_open',
  DIALOG_CLOSE: 'dialog_close',
  
  // Errors
  API_ERROR: 'api_error',
  VALIDATION_ERROR: 'validation_error',
  NETWORK_ERROR: 'network_error',
  GENERIC_ERROR: 'generic_error'
} as const

/**
 * Safe analytics tracking function with error handling
 */
export function trackEvent(event: AnalyticsEvent): void {
  try {
    // Only track in browser environment
    if (typeof window === 'undefined') {
      return
    }

    // Send to Google Analytics
    sendGAEvent('event', event.action, {
      event_category: event.category,
      event_label: event.label,
      value: event.value,
      ...event.custom_parameters
    })
  } catch (error) {
    console.warn('Analytics tracking failed:', error)
  }
}

/**
 * Track page view events
 */
export function trackPageView(pageName: string, additionalData?: Record<string, any>): void {
  trackEvent({
    action: pageName,
    category: ANALYTICS_CATEGORIES.NAVIGATION,
    label: pageName,
    custom_parameters: {
      page_name: pageName,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
  })
}

/**
 * Track authentication events
 */
export function trackAuthEvent(action: string, success: boolean, additionalData?: Record<string, any>): void {
  trackEvent({
    action,
    category: ANALYTICS_CATEGORIES.AUTHENTICATION,
    label: success ? 'success' : 'failure',
    value: success ? 1 : 0,
    custom_parameters: {
      success,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
  })
}

/**
 * Track CRUD operations
 */
export function trackCRUDEvent(
  operation: 'create' | 'read' | 'update' | 'delete',
  entityType: string,
  success: boolean,
  additionalData?: Record<string, any>
): void {
  const actionMap = {
    create: ANALYTICS_ACTIONS.CREATE_RECORD,
    read: ANALYTICS_ACTIONS.VIEW_RECORD,
    update: ANALYTICS_ACTIONS.UPDATE_RECORD,
    delete: ANALYTICS_ACTIONS.DELETE_RECORD
  }

  trackEvent({
    action: actionMap[operation],
    category: ANALYTICS_CATEGORIES.CRUD_OPERATIONS,
    label: `${operation}_${entityType}`,
    value: success ? 1 : 0,
    custom_parameters: {
      operation,
      entity_type: entityType,
      success,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
  })
}

/**
 * Track PDF generation events
 */
export function trackPDFEvent(action: string, success: boolean, additionalData?: Record<string, any>): void {
  trackEvent({
    action,
    category: ANALYTICS_CATEGORIES.PDF_GENERATION,
    label: success ? 'success' : 'failure',
    value: success ? 1 : 0,
    custom_parameters: {
      success,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
  })
}

/**
 * Track UI interaction events
 */
export function trackUIEvent(action: string, element: string, additionalData?: Record<string, any>): void {
  trackEvent({
    action,
    category: ANALYTICS_CATEGORIES.UI_INTERACTION,
    label: element,
    custom_parameters: {
      element,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
  })
}

/**
 * Track error events
 */
export function trackError(
  errorType: string,
  errorMessage: string,
  additionalData?: Record<string, any>
): void {
  trackEvent({
    action: ANALYTICS_ACTIONS.GENERIC_ERROR,
    category: ANALYTICS_CATEGORIES.ERROR,
    label: errorType,
    custom_parameters: {
      error_type: errorType,
      error_message: errorMessage,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
  })
}

/**
 * Track performance metrics
 */
export function trackPerformance(metric: string, value: number, additionalData?: Record<string, any>): void {
  trackEvent({
    action: metric,
    category: ANALYTICS_CATEGORIES.PERFORMANCE,
    value,
    custom_parameters: {
      metric,
      performance_value: value,
      timestamp: new Date().toISOString(),
      ...additionalData
    }
  })
}
