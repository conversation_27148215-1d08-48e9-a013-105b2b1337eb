"use client"

import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, User, Mail, Phone, Save } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useLanguage } from "@/contexts/language-context"
import { useDirection } from "@radix-ui/react-direction"
import Link from "next/link"

export default function ProfilePage() {
  const { t } = useLanguage()
  const dir = useDirection()
  const isRTL = dir === "rtl"

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 px-4 py-3">
        <div className="max-w-4xl mx-auto flex items-center space-x-4 rtl:space-x-reverse rtl:flex-row-reverse">
          <Link href="/">
            <Button variant="ghost" size="icon" className="hover:bg-[#E6F0FA]">
              {isRTL ? (
                <ArrowRight className="h-6 w-6 text-gray-600" />
              ) : (
                <ArrowLeft className="h-6 w-6 text-gray-600" />
              )}
            </Button>
          </Link>
          <h1 className="text-xl font-semibold text-gray-900">{t("profile.title")}</h1>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-8">
        <Card className="shadow-xl">
          <CardHeader className="text-center pb-6">
            <div className="flex justify-center mb-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src="/placeholder.svg?height=96&width=96" alt="User" />
                <AvatarFallback className="bg-[#0A66C2] text-white text-2xl">AD</AvatarFallback>
              </Avatar>
            </div>
            <CardTitle className="text-2xl">{t("profile.title")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                  <User className="h-4 w-4" />
                  <span>{t("profile.name")}</span>
                </Label>
                <Input id="name" defaultValue="Adil" className="rtl:text-right" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                  <Mail className="h-4 w-4" />
                  <span>{t("profile.email")}</span>
                </Label>
                <Input id="email" type="email" defaultValue="<EMAIL>" className="rtl:text-right" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                  <Phone className="h-4 w-4" />
                  <span>{t("profile.phone")}</span>
                </Label>
                <Input id="phone" type="tel" defaultValue="****** 567 8900" className="rtl:text-right" />
              </div>
            </div>
            <div className="flex justify-end rtl:justify-start pt-6">
              <Button className="bg-[#0A66C2] hover:bg-[#0A66C2]/90 flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                <Save className="h-4 w-4" />
                <span>{t("profile.save")}</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
