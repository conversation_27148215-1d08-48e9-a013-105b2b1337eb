import Head from 'next/head'

export function IOSSplashScreens() {
  return (
    <Head>
      {/* iOS Splash Screens */}
      <link
        rel="apple-touch-startup-image"
        href="/ios/512.png"
        media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)"
      />
      <link
        rel="apple-touch-startup-image"
        href="/ios/512.png"
        media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)"
      />
      <link
        rel="apple-touch-startup-image"
        href="/ios/512.png"
        media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)"
      />
      <link
        rel="apple-touch-startup-image"
        href="/ios/512.png"
        media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"
      />
      <link
        rel="apple-touch-startup-image"
        href="/ios/512.png"
        media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"
      />
      <link
        rel="apple-touch-startup-image"
        href="/ios/512.png"
        media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)"
      />
      
      {/* Additional iOS Meta Tags */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Adil's Coffres" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Apple Touch Icons */}
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      <link rel="apple-touch-icon" sizes="152x152" href="/ios/152.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/ios/180.png" />
      <link rel="apple-touch-icon" sizes="167x167" href="/ios/167.png" />
      
      {/* Mask Icon for Safari */}
      <link rel="mask-icon" href="/ios/512.png" color="#0A66C2" />
      
      {/* Theme Color */}
      <meta name="theme-color" content="#0A66C2" />
      <meta name="msapplication-TileColor" content="#0A66C2" />
      <meta name="msapplication-TileImage" content="/ios/144.png" />
    </Head>
  )
}
