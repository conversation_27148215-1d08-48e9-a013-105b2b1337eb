"use client"

import type React from "react"
import { useAuth } from "@/contexts/auth-context"
import { usePathname, useRouter } from "next/navigation"
import { useEffect, useState } from "react"

interface AuthWrapperProps {
  children: React.ReactNode
}

export function AuthWrapper({ children }: AuthWrapperProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const pathname = usePathname()
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)

  // Set client flag to prevent hydration mismatch
  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    // Don't redirect if we're still loading or not on client
    if (isLoading || !isClient) return

    // Allow access to signin page regardless of auth status
    if (pathname === "/signin") {
      // If user is already authenticated, redirect to home
      if (isAuthenticated) {
        router.replace("/")
        return
      }
      // Otherwise, allow access to signin page
      return
    }

    // For all other pages, require authentication
    if (!isAuthenticated) {
      // Use replace instead of push to avoid back button issues
      router.replace("/signin")
      return
    }
  }, [isAuthenticated, isLoading, pathname, router, isClient])

  // Show loading screen while checking authentication or during hydration
  if (isLoading || !isClient) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#0A66C2] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Show signin page if not authenticated (except when already on signin page)
  if (!isAuthenticated && pathname !== "/signin") {
    // Show loading while redirecting
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#0A66C2] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Show home page if authenticated and trying to access signin
  if (isAuthenticated && pathname === "/signin") {
    // Show loading while redirecting
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#0A66C2] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
