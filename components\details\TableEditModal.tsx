"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useState, useEffect } from "react"

interface TableEditModalProps {
  isOpen: boolean
  tableName: string
  tableColor: string
  isLoading: boolean
  onClose: () => void
  onTableNameChange: (name: string) => void
  onTableColorChange: (color: string) => void
  onSave: () => void
}

export function TableEditModal({
  isOpen,
  tableName,
  tableColor,
  isLoading,
  onClose,
  onTableNameChange,
  onTableColorChange,
  onSave
}: TableEditModalProps) {
  const [selectedColor, setSelectedColor] = useState(tableColor)

  // Update local color when prop changes
  useEffect(() => {
    setSelectedColor(tableColor)
  }, [tableColor])

  // Handle color change
  const handleColor<PERSON>hange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = event.target.value
    setSelectedColor(newColor)
    onTableColorChange(newColor) // Send hex code directly
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md" dir="rtl">
        <DialogHeader>
          <DialogTitle className="text-right">تعديل الجدول</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div>
            <Label className="text-right block mb-2" dir="rtl">
              اسم الجدول
            </Label>
            <Input
              value={tableName}
              onChange={(e) => onTableNameChange(e.target.value)}
              className="text-right"
              dir="rtl"
              placeholder="أدخل اسم الجدول"
            />
          </div>

          <div>
            <Label className="text-right block mb-2" dir="rtl">
              لون الجدول
            </Label>
            <div className="flex items-center space-x-3 space-x-reverse" dir="rtl">

              {/* Color Picker Input */}
              <div className="flex-1">
                <input
                  type="color"
                  value={selectedColor}
                  onChange={handleColorChange}
                  className="w-full h-12 rounded-lg border-2 border-gray-300 cursor-pointer"
                  title="اختر لون الجدول"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              إلغاء
            </Button>
            <Button
              onClick={onSave}
              disabled={isLoading}
              className="bg-[#0A66C2] hover:bg-[#0A66C2]/90"
            >
              {isLoading ? "جاري الحفظ..." : "حفظ"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
