"use client"

import { useState, useEffect } from "react"
import { Calendar, DollarSign, Save, X, FileText, MessageSquare } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useLanguage } from "@/contexts/language-context"
import { useDirection } from "@radix-ui/react-direction"

interface Record {
  id: string
  nameKey: string
  value: number
  date: string
  dateReturn?: string
  commentaire?: string | null
}

interface EditRecordDialogProps {
  record: Record | null
  isOpen: boolean
  onClose: () => void
  onSave: (record: Record) => void
  mode?: "add" | "edit" // Add mode prop
}

export function EditRecordDialog({ record, isOpen, onClose, onSave, mode = "edit" }: EditRecordDialogProps) {
  const { t } = useLanguage()
  const dir = useDirection()
  const [formData, setFormData] = useState<Record | null>(null)
  const [showValidationError, setShowValidationError] = useState(false)
  const [displayValue, setDisplayValue] = useState<string>("")
  const [displayDateOut, setDisplayDateOut] = useState<string>("")
  const [displayDateReturn, setDisplayDateReturn] = useState<string>("")

  // Helper function to format number with thousands separator
  const formatNumberDisplay = (value: number | string): string => {
    if (value === "" || value === null || value === undefined) return ""
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return ""

    // Convert to string and add dots every 3 digits from right to left
    const numStr = Math.floor(numValue).toString()
    return numStr.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
  }

  // Helper function to parse formatted number back to integer
  const parseFormattedNumber = (formattedValue: string): number => {
    if (!formattedValue) return 0
    // Remove all dots (thousands separators) and parse as integer
    const cleanValue = formattedValue.replace(/\./g, '')
    return parseInt(cleanValue) || 0
  }

  // Helper function to format date input as JJ/MM/AAAA
  const formatDateInput = (value: string, previousValue: string = ""): string => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '')
    const previousDigits = previousValue.replace(/\D/g, '')

    // Check if this is a deletion operation
    const isDeleting = digits.length < previousDigits.length

    // Handle smart deletion: only when deleting first digit of a segment (not manual slash deletion)
    if (isDeleting) {
      // Check if only a slash was removed (digits count same, but value length different)
      const slashOnlyDeleted = digits.length === previousDigits.length && value.length < previousValue.length

      // Only apply smart deletion if a digit was removed, not just a slash
      if (!slashOnlyDeleted) {
        // If we had "12/3X" and now have "12X" (deleted first digit of MM), return "12"
        if (previousDigits.length === 3 && digits.length === 2) {
          return digits // "12" - cursor will be after 2
        }
        // If we had "12/34/5XXX" and now have "1234XXX" (deleted first digit of AAAA), return "12/34"
        if (previousDigits.length >= 5 && digits.length === 4) {
          return `${digits.slice(0, 2)}/${digits.slice(2, 4)}` // "12/34" - cursor will be after 4
        }
      }
    }

    // Apply JJ/MM/AAAA formatting - add slash immediately after 2nd digit
    if (digits.length <= 1) {
      return digits                    // "1"
    } else if (digits.length === 2) {
      // After 2nd digit in JJ, add slash immediately: "12" = "12/"
      return `${digits}/`
    } else if (digits.length === 3) {
      // MM part starts: "12/3"
      return `${digits.slice(0, 2)}/${digits.slice(2)}`
    } else if (digits.length === 4) {
      // After 2nd digit in MM, add slash immediately: "1234" = "12/34/"
      return `${digits.slice(0, 2)}/${digits.slice(2, 4)}/`
    } else {
      // Year part: "12345678" = "12/34/5678"
      return `${digits.slice(0, 2)}/${digits.slice(2, 4)}/${digits.slice(4, 8)}`
    }
  }

  // Helper function to convert DD/MM/YYYY to YYYY-MM-DD for API
  const convertToApiDate = (displayDate: string): string => {
    if (!displayDate || displayDate.length < 10) return ""
    const parts = displayDate.split('/')
    if (parts.length !== 3) return ""
    const [day, month, year] = parts
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
  }

  // Helper function to convert YYYY-MM-DD to DD/MM/YYYY for display
  const convertToDisplayDate = (apiDate: string): string => {
    if (!apiDate) return ""
    const parts = apiDate.split('-')
    if (parts.length !== 3) return ""
    const [year, month, day] = parts
    return `${day}/${month}/${year}`
  }

  useEffect(() => {
    if (record) {
      // For new records, start with empty value instead of 0
      const initialData = mode === "add" && record.value === 0
        ? { ...record, value: "" as any, commentaire: record.commentaire || "" } // Use empty string for new records
        : { ...record, commentaire: record.commentaire || "" }
      setFormData(initialData)

      // Set display value for the formatted input
      if (mode === "add" && record.value === 0) {
        setDisplayValue("")
      } else {
        setDisplayValue(formatNumberDisplay(record.value))
      }

      // Set display dates
      setDisplayDateOut(convertToDisplayDate(record.date))
      setDisplayDateReturn(convertToDisplayDate(record.dateReturn || ""))

      setShowValidationError(false) // Reset validation error when dialog opens
    }
  }, [record, mode])

  const handleSave = () => {
    if (formData && isFormValid) {
      // Parse the formatted display value back to integer before saving
      const finalData = {
        ...formData,
        value: parseFormattedNumber(displayValue),
        date: convertToApiDate(displayDateOut),
        dateReturn: convertToApiDate(displayDateReturn)
      }
      onSave(finalData)
      onClose()
      setShowValidationError(false)
    } else {
      // Show validation error only when user tries to save
      setShowValidationError(true)
    }
  }

  // Validation: Check if name is not empty and amount is greater than 0
  const isFormValid = formData &&
    formData.nameKey.trim() !== "" &&
    parseFormattedNumber(displayValue) > 0

  if (!record || !formData) return null

  // Dynamic text based on mode
  const isAddMode = mode === "add"
  const dialogTitle = isAddMode ? "إضافة سجل جديد" : "تعديل السجل"
  const saveButtonText = isAddMode ? "إضافة السجل" : "حفظ التغييرات"

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
            <Save className="h-5 w-5" />
            <span>{dialogTitle}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Name and Montant in separate inputs */}
          <div className="grid grid-cols-2 gap-4">
            {/* Record Name */}
            <div className="space-y-2">
              <Label htmlFor="name" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                <FileText className="h-4 w-4" />
                <span>الاسم <span className="text-red-500">*</span></span>
              </Label>
              <Input
                id="name"
                value={formData.nameKey.startsWith("record.") ? t(formData.nameKey) : formData.nameKey}
                onChange={(e) => setFormData({ ...formData, nameKey: e.target.value })}
                className={`rtl:text-right ${formData.nameKey.trim() === "" && showValidationError ? "border-red-300 focus:border-red-500" : ""}`}
                placeholder="أدخل الاسم"
                required
              />
            </div>

            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="value" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                <DollarSign className="h-4 w-4" />
                <span>المبلغ <span className="text-red-500">*</span></span>
              </Label>
              <Input
                id="value"
                type="text"
                value={displayValue}
                onChange={(e) => {
                  const inputValue = e.target.value

                  // Allow only digits, remove any other characters (including existing dots)
                  const cleanInput = inputValue.replace(/[^\d]/g, '')

                  // If empty, set empty display and empty form value
                  if (cleanInput === "") {
                    setDisplayValue("")
                    setFormData({ ...formData, value: "" as any })
                    return
                  }

                  // Parse the number
                  const numValue = parseInt(cleanInput) || 0

                  // Format and display the number with thousands separators
                  if (numValue > 0) {
                    const formatted = formatNumberDisplay(numValue)
                    setDisplayValue(formatted)
                    setFormData({ ...formData, value: numValue })
                  } else {
                    setDisplayValue("")
                    setFormData({ ...formData, value: "" as any })
                  }
                }}
                className={`rtl:text-right ${(parseFormattedNumber(displayValue) <= 0 || displayValue === "") && showValidationError ? "border-red-300 focus:border-red-500" : ""}`}
                placeholder="0"
                step="0.01"
                required
              />
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dateOut" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                <Calendar className="h-4 w-4" />
                <span>تاريخ الخروج</span>
              </Label>
              <div className="relative">
                <Input
                  id="dateOut"
                  type="text"
                  value={displayDateOut}
                  onChange={(e) => {
                    const inputValue = e.target.value
                    const formatted = formatDateInput(inputValue, displayDateOut)
                    setDisplayDateOut(formatted)
                    setFormData({ ...formData, date: convertToApiDate(formatted) })
                  }}
                  onKeyDown={(e) => {
                    // Only allow numbers, backspace, delete, arrow keys, and tab
                    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab']
                    const isNumber = /^[0-9]$/.test(e.key)

                    if (!isNumber && !allowedKeys.includes(e.key)) {
                      e.preventDefault()
                    }
                  }}
                  placeholder="JJ/MM/AAAA"
                  maxLength={10}
                  className="rtl:text-right pr-8"
                />
                {displayDateOut && (
                  <button
                    type="button"
                    onClick={() => {
                      setDisplayDateOut("")
                      setFormData({ ...formData, date: "" })
                    }}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dateReturn" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
                <Calendar className="h-4 w-4" />
                <span>تاريخ الإرجاع</span>
              </Label>
              <div className="relative">
                <Input
                  id="dateReturn"
                  type="text"
                  value={displayDateReturn}
                  onChange={(e) => {
                    const inputValue = e.target.value
                    const formatted = formatDateInput(inputValue, displayDateReturn)
                    setDisplayDateReturn(formatted)
                    setFormData({ ...formData, dateReturn: convertToApiDate(formatted) })
                  }}
                  onKeyDown={(e) => {
                    // Only allow numbers, backspace, delete, arrow keys, and tab
                    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab']
                    const isNumber = /^[0-9]$/.test(e.key)

                    if (!isNumber && !allowedKeys.includes(e.key)) {
                      e.preventDefault()
                    }
                  }}
                  placeholder="JJ/MM/AAAA"
                  maxLength={10}
                  className="rtl:text-right pr-8"
                />
                {displayDateReturn && (
                  <button
                    type="button"
                    onClick={() => {
                      setDisplayDateReturn("")
                      setFormData({ ...formData, dateReturn: "" })
                    }}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Comment Field */}
          <div className="space-y-2">
            <Label htmlFor="commentaire" className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
              <MessageSquare className="h-4 w-4" />
              <span>التعليق</span>
            </Label>
            <Textarea
              id="commentaire"
              value={formData?.commentaire || ""}
              onChange={(e) => setFormData({ ...formData, commentaire: e.target.value })}
              className="rtl:text-right min-h-[80px]"
              placeholder="أدخل تعليق (اختياري)"
              dir="rtl"
            />
          </div>
        </div>

        {/* Validation Message - Only show after user tries to save */}
        {showValidationError && !isFormValid && (
          <div className="text-sm text-red-600 text-center bg-red-50 p-2 rounded-md" dir="rtl">
            يرجى ملء الاسم وإدخال مبلغ أكبر من صفر
          </div>
        )}

        <div className="flex justify-end space-x-2 rtl:space-x-reverse rtl:flex-row-reverse">
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
            إلغاء
          </Button>
          <Button
            onClick={handleSave}
            className="bg-[#0A66C2] hover:bg-[#0A66C2]/90"
          >
            <Save className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
            {saveButtonText}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
