"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { X, Download, Smartphone } from "lucide-react"

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

export function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [isIOS, setIsIOS] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  useEffect(() => {
    // Check if running on iOS
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    setIsIOS(iOS)

    // Check if already installed (standalone mode)
    const standalone = window.matchMedia('(display-mode: standalone)').matches
    setIsStandalone(standalone)

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      
      // Don't show prompt if already dismissed recently
      const lastDismissed = localStorage.getItem('pwa-install-dismissed')
      const daysSinceDismissed = lastDismissed 
        ? (Date.now() - parseInt(lastDismissed)) / (1000 * 60 * 60 * 24)
        : 999

      if (daysSinceDismissed > 7) { // Show again after 7 days
        setShowInstallPrompt(true)
      }
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

    // For iOS, show manual install instructions after some time
    if (iOS && !standalone) {
      const timer = setTimeout(() => {
        const lastDismissed = localStorage.getItem('pwa-install-dismissed-ios')
        const daysSinceDismissed = lastDismissed 
          ? (Date.now() - parseInt(lastDismissed)) / (1000 * 60 * 60 * 24)
          : 999

        if (daysSinceDismissed > 7) {
          setShowInstallPrompt(true)
        }
      }, 10000) // Show after 10 seconds

      return () => clearTimeout(timer)
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    }
  }, [])

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        console.log('PWA installed')
      }
      
      setDeferredPrompt(null)
      setShowInstallPrompt(false)
    }
  }

  const handleDismiss = () => {
    setShowInstallPrompt(false)
    localStorage.setItem(
      isIOS ? 'pwa-install-dismissed-ios' : 'pwa-install-dismissed', 
      Date.now().toString()
    )
  }

  // Don't show if already installed
  if (isStandalone) return null

  // Don't show if user dismissed recently
  if (!showInstallPrompt) return null

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 mx-auto max-w-sm shadow-lg border-2 border-[#0A66C2]">
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-gradient-to-r from-[#0A66C2] to-blue-600 rounded-lg flex items-center justify-center">
              <Smartphone className="h-5 w-5 text-white" />
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900">
              Install Adil's Coffres
            </h3>
            
            {isIOS ? (
              <div className="mt-1">
                <p className="text-xs text-gray-600 mb-2">
                  Add to your home screen for the best experience
                </p>
                <p className="text-xs text-gray-500">
                  Tap <span className="font-mono bg-gray-100 px-1 rounded">⎙</span> then "Add to Home Screen"
                </p>
              </div>
            ) : (
              <p className="mt-1 text-xs text-gray-600">
                Get quick access and work offline
              </p>
            )}
            
            <div className="mt-3 flex space-x-2">
              {!isIOS && deferredPrompt && (
                <Button 
                  size="sm" 
                  onClick={handleInstallClick}
                  className="bg-[#0A66C2] hover:bg-blue-700 text-white"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Install
                </Button>
              )}
              
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={handleDismiss}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-3 w-3 mr-1" />
                Dismiss
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
