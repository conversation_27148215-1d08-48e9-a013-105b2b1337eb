"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { trackEvent, trackPageView, trackError, ANALYTICS_ACTIONS, ANALYTICS_CATEGORIES } from '@/lib/utils/analytics'
import toast from 'react-hot-toast'

interface AnalyticsContextType {
  isInitialized: boolean
  debugMode: boolean
  setDebugMode: (enabled: boolean) => void
  trackEvent: typeof trackEvent
  trackPageView: typeof trackPageView
  trackError: typeof trackError
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined)

interface AnalyticsProviderProps {
  children: React.ReactNode
}

export function AnalyticsProvider({ children }: AnalyticsProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)
  const [debugMode, setDebugMode] = useState(false)

  useEffect(() => {
    // Initialize analytics
    const initializeAnalytics = () => {
      try {
        // Check if we're in browser environment
        if (typeof window === 'undefined') {
          return
        }

        // Track app initialization
        trackEvent({
          action: 'app_initialized',
          category: ANALYTICS_CATEGORIES.PERFORMANCE,
          label: 'application_start',
          custom_parameters: {
            environment: process.env.NODE_ENV,
            user_agent: navigator.userAgent,
            screen_resolution: `${screen.width}x${screen.height}`,
            timestamp: new Date().toISOString()
          }
        })

        setIsInitialized(true)
      } catch (error) {
        trackError('analytics_init_error', error instanceof Error ? error.message : 'Unknown error')
      }
    }

    initializeAnalytics()
  }, [])

  // Enhanced tracking functions with debug mode support
  const enhancedTrackEvent = (event: Parameters<typeof trackEvent>[0]) => {
    try {
      trackEvent(event)
    } catch (error) {
      console.error('Analytics tracking error:', error)
    }
  }

  const enhancedTrackPageView = (pageName: string, additionalData?: Record<string, any>) => {
    try {
      trackPageView(pageName, additionalData)
    } catch (error) {
      console.error('Page view tracking error:', error)
    }
  }

  const enhancedTrackError = (errorType: string, errorMessage: string, additionalData?: Record<string, any>) => {
    try {
      trackError(errorType, errorMessage, additionalData)
    } catch (error) {
      console.error('Error tracking error:', error)
    }
  }

  const contextValue: AnalyticsContextType = {
    isInitialized,
    debugMode,
    setDebugMode,
    trackEvent: enhancedTrackEvent,
    trackPageView: enhancedTrackPageView,
    trackError: enhancedTrackError
  }

  return (
    <AnalyticsContext.Provider value={contextValue}>
      {children}
    </AnalyticsContext.Provider>
  )
}

export function useAnalytics() {
  const context = useContext(AnalyticsContext)
  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider')
  }
  return context
}

// Convenience hooks for specific analytics use cases
export function usePageTracking() {
  const { trackPageView } = useAnalytics()
  
  const trackPage = (pageName: string, additionalData?: Record<string, any>) => {
    useEffect(() => {
      trackPageView(pageName, additionalData)
    }, [pageName])
  }
  
  return { trackPage }
}

export function useEventTracking() {
  const { trackEvent } = useAnalytics()
  
  return {
    trackButtonClick: (buttonName: string, additionalData?: Record<string, any>) => {
      trackEvent({
        action: ANALYTICS_ACTIONS.BUTTON_CLICK,
        category: ANALYTICS_CATEGORIES.UI_INTERACTION,
        label: buttonName,
        custom_parameters: additionalData
      })
    },
    
    trackDialogOpen: (dialogName: string, additionalData?: Record<string, any>) => {
      trackEvent({
        action: ANALYTICS_ACTIONS.DIALOG_OPEN,
        category: ANALYTICS_CATEGORIES.UI_INTERACTION,
        label: dialogName,
        custom_parameters: additionalData
      })
    },
    
    trackDialogClose: (dialogName: string, additionalData?: Record<string, any>) => {
      trackEvent({
        action: ANALYTICS_ACTIONS.DIALOG_CLOSE,
        category: ANALYTICS_CATEGORIES.UI_INTERACTION,
        label: dialogName,
        custom_parameters: additionalData
      })
    }
  }
}
