"use client"

import { useState } from "react"
import { Search, ArrowLeft, ArrowR<PERSON>, Clock } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/contexts/language-context"
import { useDirection } from "@radix-ui/react-direction"
import Link from "next/link"

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const { t } = useLanguage()
  const dir = useDirection()
  const isRTL = dir === "rtl"

  const suggestedKeywords = [
    t("home.category.a"),
    t("home.category.b"),
    t("home.category.c"),
    t("home.category.d"),
    t("home.records"),
    "Values",
    "Reports",
    "Analytics",
  ]

  const recentSearches = [
    `${t("home.category.a")} ${t("home.records")}`,
    `${t("home.remaining")} values`,
    "Monthly reports",
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-6xl mx-auto flex items-center space-x-4 rtl:space-x-reverse rtl:flex-row-reverse">
          <Link href="/">
            <Button variant="ghost" size="icon" className="hover:bg-[#E6F0FA]">
              {isRTL ? (
                <ArrowRight className="h-6 w-6 text-gray-600" />
              ) : (
                <ArrowLeft className="h-6 w-6 text-gray-600" />
              )}
            </Button>
          </Link>
          <h1 className="text-xl font-semibold text-gray-900">{t("search.title")}</h1>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 py-6">
        {/* Editable Search Bar */}
        <div className="relative w-full mb-8">
          <div className="flex items-center bg-white rounded-xl px-4 py-4 border-2 border-[#0A66C2] shadow-sm rtl:flex-row-reverse">
            <Search className="h-5 w-5 text-[#0A66C2] mr-3 rtl:mr-0 rtl:ml-3" />
            <Input
              type="text"
              placeholder={t("search.placeholder")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="border-0 text-lg focus-visible:ring-0 focus-visible:ring-offset-0 p-0 rtl:text-right"
              autoFocus
            />
          </div>
        </div>

        {/* Search Results (when there's a query) */}
        {searchQuery && (
          <div className="mt-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              {t("search.results")} "{searchQuery}"
            </h2>
            <div className="text-gray-500 text-center py-8">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>{t("search.no.results")}</p>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
