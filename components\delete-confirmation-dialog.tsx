"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { useLanguage } from "@/contexts/language-context"

interface DeleteConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  recordName: string
}

export function DeleteConfirmationDialog({ isOpen, onClose, onConfirm, recordName }: DeleteConfirmationDialogProps) {
  const { t } = useLanguage()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 rtl:space-x-reverse rtl:flex-row-reverse text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Delete Record</span>
          </DialogTitle>
          <DialogDescription className="text-left rtl:text-right">
            Are you sure you want to delete "{recordName}"? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-end space-x-2 rtl:space-x-reverse rtl:flex-row-reverse pt-4">
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
            Cancel
          </Button>
          <Button onClick={onConfirm} variant="destructive">
            <Trash2 className="h-4 w-4 mr-2 rtl:mr-0 rtl:ml-2" />
            Delete
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
