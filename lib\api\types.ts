// API Response Types

export type ApiResponse<T> = T 

export interface ApiError {
  message: string
  code: string
  details?: any
}

// User Types
export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: string
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface AuthResponse {
  accessToken: string
  refreshToken: string
}

export interface LoginResponse {
  accessToken: string
  refreshToken: string
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface RefreshTokenResponse {
  accessToken: string
  refreshToken: string
}

export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
}

// Backend Entity Types (mapped from Java entity fields to English)
export interface BackendCoffreEntity {
  id: number                        // private Integer id;
  coffreTotal: number              // private Double coffreTotal;
  coffreName: string               // private String coffreName;
  dateDerniereModification: string // private LocalDate dateDerniereModification;
  coffreColor?: string                // private String colorHex; (new field for table color)
}

// Frontend Coffre Types (for UI display)
export interface Coffre {
  id: number
  name: string                     // mapped from coffreName
  nameKey: string
  createdDate: string
  lastUpdated: string             // mapped from dateDerniereModification
  recordsCount: number
  initialValue: number            // mapped from coffreTotal
  actualValue: number
  classification: string
  gradient: string
  textColor: string
  coffreColor?: string               // mapped from backend colorHex
  status: "active" | "inactive" | "archived"
  userId: string
}

export interface CoffresResponse {
  coffres: Coffre[]
  total: number
  page: number
  limit: number
}

// Record Types
export interface Record {
  id: string
  coffreId: number
  nameKey: string
  value: number
  date: string
  time: string
  commentKey: string
  status: "completed" | "pending" | "cancelled"
  priority: "high" | "medium" | "low"
  createdAt: string
  updatedAt: string
}

export interface RecordsResponse {
  records: Record[]
  total: number
  page: number
  limit: number
}

export interface CreateRecordRequest {
  coffreId: number
  nameKey: string
  value: number
  date: string
  time: string
  commentKey: string
  status: "completed" | "pending" | "cancelled"
  priority: "high" | "medium" | "low"
}

export interface UpdateRecordRequest extends Partial<CreateRecordRequest> {
  id: string
}

// Utility function to map backend entity to frontend Coffre
export function mapBackendCoffreToFrontend(
  backendCoffre: BackendCoffreEntity,
  additionalData?: Partial<Coffre>
): Coffre {
  return {
    id: backendCoffre.id,
    name: backendCoffre.coffreName,
    nameKey: additionalData?.nameKey || `coffre.${backendCoffre.id}`,
    createdDate: additionalData?.createdDate || "",
    lastUpdated: backendCoffre.dateDerniereModification,
    recordsCount: additionalData?.recordsCount || 0,
    initialValue: backendCoffre.coffreTotal,
    actualValue: additionalData?.actualValue || backendCoffre.coffreTotal,
    classification: additionalData?.classification || "Standard",
    gradient: additionalData?.gradient || "bg-blue-500",
    textColor: additionalData?.textColor || "text-white",
    status: additionalData?.status || "active",
    userId: additionalData?.userId || "",
  }
}
