"use client"

import { Edit, Trash2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/contexts/language-context"

interface RecordsTableProps {
  records: any[]
  isPriceHidden: boolean
  onEditRecord: (record: any) => void
  onDeleteRecord: (record: any) => void
  formatDate: (date: string) => string
  formatCurrencyFrench: (amount: number) => string
}

export function RecordsTable({
  records,
  isPriceHidden,
  onEditRecord,
  onDeleteRecord,
  formatDate,
  formatCurrencyFrench
}: RecordsTableProps) {
  const { t, isRTL } = useLanguage()

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-200">
            <th className={`py-3 px-4 text-sm font-medium text-gray-700 ${isRTL ? "text-right" : "text-left"}`}>
              {t("table.actions")}
            </th>
            <th className={`py-3 px-4 text-sm font-medium text-gray-700 ${isRTL ? "text-right" : "text-left"}`}>
              تاريخ تحصيل
            </th>
            <th className={`py-3 px-4 text-sm font-medium text-gray-700 ${isRTL ? "text-right" : "text-left"}`}>
              تاريخ الخروج
            </th>
            <th className={`py-3 px-4 text-sm font-medium text-gray-700 ${isRTL ? "text-right" : "text-left"}`}>
              {t("table.amount")}
            </th>
            <th className={`py-3 px-4 text-sm font-medium text-gray-700 ${isRTL ? "text-right" : "text-left"}`}>
              {t("table.name")}
            </th>
          </tr>
        </thead>
        <tbody>
          {records.map((record) => {
            const recordName = record.nameKey.startsWith("record.") ? t(record.nameKey) : record.nameKey
            
            return (
              <tr key={record.id} className="border-b border-gray-100 hover:bg-gray-50">
                <td className="py-3 px-4">
                  <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditRecord(record)}
                      className="p-1 h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteRecord(record)}
                      className="p-1 h-8 w-8 text-red-600 hover:text-red-800 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
                <td className={`py-3 px-4 text-sm text-gray-900 ${isRTL ? "text-right" : "text-left"}`}>
                  {record.dateReturn ? (
                    <span className="font-medium date-french">{formatDate(record.dateReturn)}</span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className={`py-3 px-4 text-sm text-gray-900 ${isRTL ? "text-right" : "text-left"}`}>
                  {record.date ? (
                    <span className="font-medium date-french">{formatDate(record.date)}</span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className={`py-3 px-4 text-sm font-semibold text-gray-900 ${isRTL ? "text-right" : "text-left"}`}>
                  <span className="number-french">
                    {isPriceHidden ? "••••••" : formatCurrencyFrench(record.value)}
                  </span>
                </td>
                <td className={`py-3 px-4 text-sm font-medium text-gray-900 ${isRTL ? "text-right" : "text-left"}`} dir="rtl">
                  <span style={{direction: 'rtl', textAlign: 'right', unicodeBidi: 'embed'}}>
                    {recordName}
                  </span>
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  )
}
