import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import { GoogleAnalytics } from '@next/third-parties/google'
import { LanguageProvider } from "@/contexts/language-context"
import { AuthProvider } from "@/contexts/auth-context"
import { <PERSON>piProvider } from "@/contexts/api-context"
import { AnalyticsProvider } from "@/contexts/analytics-context"
import { AuthWrapper } from "@/components/auth-wrapper"
import { PWAInstallPrompt } from "@/components/pwa-install-prompt"
import { ClientOnly } from "@/components/client-only"
import { AnalyticsDebugPanel } from "@/components/analytics-debug"
import { Toaster } from "react-hot-toast"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover'
}

export const metadata: Metadata = {
  title: "Adil's Coffres",
  description: "Personal financial management system - Système de gestion financière personnelle",
  keywords: "finance, coffres, gestion, argent, épargne, financial management, savings",
  authors: [{ name: "Adil" }],
  creator: "Adil",
  publisher: "Adil's Coffres",
  generator: 'Next.js',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: "Adil's Coffres",
    startupImage: [
      {
        url: '/ios/180.png',
        media: '(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)',
      },
      {
        url: '/ios/192.png',
        media: '(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)',
      },
      {
        url: '/ios/512.png',
        media: '(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)',
      }
    ]
  },
  icons: {
    apple: [
      { url: '/ios/180.png', sizes: '180x180', type: 'image/png' },
      { url: '/ios/152.png', sizes: '152x152', type: 'image/png' },
      { url: '/ios/120.png', sizes: '120x120', type: 'image/png' },
    ]
  },

}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr">
      <body className={inter.className}>
        <ApiProvider>
          <AuthProvider>
            <AnalyticsProvider>
              <LanguageProvider>
                  <AuthWrapper>
                    {children}
                  </AuthWrapper>
                  <PWAInstallPrompt />
                  <Toaster
                  position="top-center"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                      fontSize: '14px',
                      fontWeight: '500',
                      borderRadius: '8px',
                      padding: '12px 16px',
                      maxWidth: '90vw',
                      wordBreak: 'break-word',
                    },
                    success: {
                      duration: 3000,
                      style: {
                        background: '#10b981',
                        color: '#fff',
                      },
                      iconTheme: {
                        primary: '#fff',
                        secondary: '#10b981',
                      },
                    },
                    error: {
                      duration: 5000,
                      style: {
                        background: '#ef4444',
                        color: '#fff',
                      },
                      iconTheme: {
                        primary: '#fff',
                        secondary: '#ef4444',
                      },
                    },
                  }}
                  containerStyle={{
                    top: 20,
                    left: 20,
                    bottom: 20,
                    right: 20,
                  }}
                />
              </LanguageProvider>
            </AnalyticsProvider>
          </AuthProvider>
        </ApiProvider>
      </body>
      <GoogleAnalytics gaId="G-9GYWTG1CV4" />
    </html>
  )
}
