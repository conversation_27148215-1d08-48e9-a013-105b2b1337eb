// Table Types
export interface Table {
  id: number
  coffreName: string
  coffreTotal: number
  dateDerniereModification: string
  coffreColor: string
  coffreNameIhm: string
}

export type TablesResponse = Table[];  // or just use Table[] directly
export type TableResponse = Table;  // or just use Table[] directly

export interface CreateTableRequest {
  coffreName: string
  coffreTotal: number
  coffreColor: string
}

export interface UpdateTableRequest {
  coffreName?: string
  coffreNameIhm?: string  // Display name for UI
  coffreTotal?: number
  coffreColor?: string
}

// Record Types
export interface RecordTable {
  id: number
  nomComplet: string
  montant: number
  dateSortie: string
  dateRetour: string
  coffreFort?: string | null
  commentaire: string | null
}

export interface RecordsResponse {
  elements: RecordTable[]
  rest: number
  coffreTotal: number
  montantConsommee: number
  lastDateModification: string
  totalPages: number
  totalElements: number
  size: number
  number: number
  first: boolean
  last: boolean
  numberOfElements: number
}

export interface RecordResponse extends RecordTable {}

export interface CreateRecordRequest {
  nomComplet: string
  montant: number
  dateSortie: string
  dateRetour: string
  commentaire?: string | null
  coffreFort?: string | null
}

export interface UpdateRecordRequest {
  nomComplet?: string
  montant?: number
  dateSortie?: string
  dateRetour?: string
  commentaire?: string | null
  id: number
  coffreFort?: string | null
}

// Search Types
export interface SearchParams {
  nom?: string
  montant?: number
}

// Error Types
export interface ApiError {
  error: string
  details?: string
}
