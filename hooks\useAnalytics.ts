/**
 * Enhanced Analytics Hook
 * Provides convenient methods for tracking various types of events
 */

import { useAnalytics } from '@/contexts/analytics-context'
import { 
  trackAuthEvent, 
  trackCRUDEvent, 
  trackPDFEvent, 
  trackUIEvent,
  ANALYTICS_ACTIONS,
  ANALYTICS_CATEGORIES 
} from '@/lib/utils/analytics'

export function useEnhancedAnalytics() {
  const analytics = useAnalytics()

  return {
    ...analytics,
    
    // Authentication tracking
    trackSignInAttempt: (email?: string) => {
      trackAuthEvent(ANALYTICS_ACTIONS.SIGN_IN_ATTEMPT, false, { email })
    },
    
    trackSignInSuccess: (email?: string) => {
      trackAuthEvent(ANALYTICS_ACTIONS.SIGN_IN_SUCCESS, true, { email })
    },
    
    trackSignInFailure: (error: string, email?: string) => {
      trackAuthEvent(ANALYTICS_ACTIONS.SIGN_IN_FAILURE, false, { error, email })
    },
    
    trackSignOut: () => {
      trackAuthEvent(ANALYTICS_ACTIONS.SIGN_OUT, true)
    },
    
    trackSessionTimeout: () => {
      trackAuthEvent(ANALYTICS_ACTIONS.SESSION_TIMEOUT, true)
    },

    // Navigation tracking
    trackNavigation: (from: string, to: string, method: 'click' | 'back' | 'direct' = 'click') => {
      analytics.trackEvent({
        action: `${from} _to_ ${to}`,
        category: ANALYTICS_CATEGORIES.NAVIGATION,
        label: `${from}_to_${to}`,
        custom_parameters: {
          from_page: from,
          to_page: to,
          navigation_method: method
        }
      })
    },

    trackTableClick: (tableId: string, tableName: string) => {
      analytics.trackEvent({
        action: ANALYTICS_ACTIONS.NAVIGATE_TO_DETAILS,
        category: ANALYTICS_CATEGORIES.NAVIGATION,
        label: tableName,
        custom_parameters: {
          table_id: tableId,
          table_name: tableName
        }
      })
    },

    // CRUD operations tracking
    trackRecordCreate: (tableId: string, success: boolean, error?: string) => {
      trackCRUDEvent('create', 'record', success, { 
        table_id: tableId, 
        error: error || undefined 
      })
    },

    trackRecordUpdate: (recordId: string, tableId: string, success: boolean, error?: string) => {
      trackCRUDEvent('update', 'record', success, { 
        record_id: recordId,
        table_id: tableId, 
        error: error || undefined 
      })
    },

    trackRecordDelete: (recordId: string, tableId: string, success: boolean, error?: string) => {
      trackCRUDEvent('delete', 'record', success, { 
        record_id: recordId,
        table_id: tableId, 
        error: error || undefined 
      })
    },

    trackRecordView: (recordId: string, tableId: string) => {
      trackCRUDEvent('read', 'record', true, { 
        record_id: recordId,
        table_id: tableId 
      })
    },

    trackTableUpdate: (tableId: string, success: boolean, changes: string[], error?: string) => {
      trackCRUDEvent('update', 'table', success, { 
        table_id: tableId,
        changes,
        error: error || undefined 
      })
    },

    // Search tracking
    trackSearch: (query: string, resultsCount: number, tableId?: string) => {
      analytics.trackEvent({
        action: ANALYTICS_ACTIONS.SEARCH_RECORDS,
        category: ANALYTICS_CATEGORIES.USER_INTERACTION,
        label: 'search_performed',
        value: resultsCount,
        custom_parameters: {
          search_query: query,
          results_count: resultsCount,
          table_id: tableId
        }
      })
    },

    // PDF generation tracking
    trackPDFDownloadAttempt: (tableId: string, tableName: string, recordCount: number) => {
      trackPDFEvent(ANALYTICS_ACTIONS.PDF_DOWNLOAD_ATTEMPT, false, {
        table_id: tableId,
        table_name: tableName,
        record_count: recordCount
      })
    },

    trackPDFDownloadSuccess: (tableId: string, tableName: string, fileSize?: number) => {
      trackPDFEvent(ANALYTICS_ACTIONS.PDF_DOWNLOAD_SUCCESS, true, {
        table_id: tableId,
        table_name: tableName,
        file_size: fileSize
      })
    },

    trackPDFDownloadFailure: (tableId: string, error: string) => {
      trackPDFEvent(ANALYTICS_ACTIONS.PDF_DOWNLOAD_FAILURE, false, {
        table_id: tableId,
        error
      })
    },

    // UI interaction tracking
    trackButtonClick: (buttonName: string, location: string, additionalData?: Record<string, any>) => {
      trackUIEvent(ANALYTICS_ACTIONS.BUTTON_CLICK, buttonName, {
        location,
        ...additionalData
      })
    },

    trackToggleVisibility: (element: string, isVisible: boolean, location: string) => {
      trackUIEvent(ANALYTICS_ACTIONS.TOGGLE_VISIBILITY, element, {
        is_visible: isVisible,
        location
      })
    },

    trackLanguageChange: (fromLanguage: string, toLanguage: string) => {
      trackUIEvent(ANALYTICS_ACTIONS.LANGUAGE_CHANGE, 'language_switcher', {
        from_language: fromLanguage,
        to_language: toLanguage
      })
    },

    trackMenuOpen: (menuType: string, location: string) => {
      trackUIEvent(ANALYTICS_ACTIONS.MENU_OPEN, menuType, {
        location
      })
    },

    trackDialogInteraction: (dialogName: string, action: 'open' | 'close', location: string) => {
      const analyticsAction = action === 'open' ? ANALYTICS_ACTIONS.DIALOG_OPEN : ANALYTICS_ACTIONS.DIALOG_CLOSE
      trackUIEvent(analyticsAction, dialogName, {
        location
      })
    },

    // Refresh tracking
    trackRefresh: (location: string, type: 'manual' | 'automatic' = 'manual') => {
      analytics.trackEvent({
        action: ANALYTICS_ACTIONS.REFRESH_TABLE,
        category: ANALYTICS_CATEGORIES.USER_INTERACTION,
        label: location,
        custom_parameters: {
          refresh_type: type,
          location
        }
      })
    },

    // Error tracking with context
    trackErrorWithContext: (errorType: string, errorMessage: string, context: Record<string, any>) => {
      analytics.trackError(errorType, errorMessage, {
        context,
        user_agent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown',
        url: typeof window !== 'undefined' ? window.location.href : 'unknown'
      })
    }
  }
}
