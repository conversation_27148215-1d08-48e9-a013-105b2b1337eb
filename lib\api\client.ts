import { tokenStorage } from "@/lib/utils/storage"
import type { ApiResponse, RefreshTokenRequest, RefreshTokenResponse } from "./types"

export class ApiClient {
  private baseURL: string
  private isRefreshing: boolean = false
  private refreshPromise: Promise<string> | null = null

  constructor(baseURL?: string) {
    // Use backend URL when not in mock mode, otherwise use Next.js API routes
    const isMockMode = process.env.NEXT_PUBLIC_USE_MOCK_API === "true"
    this.baseURL = baseURL || (isMockMode
      ? "/api/v1"  // Use Next.js API routes for mock mode
      : process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8080/api/v1"
    )
  }

  // Get current access token
  private getAccessToken(): string | null {
    return tokenStorage.getAccessToken()
  }

  // Set tokens in storage
  setTokens(accessToken: string, refreshToken: string) {
    tokenStorage.setTokens(accessToken, refreshToken)
    console.log("🔐 JWT tokens stored")
  }

  // Clear all tokens
  clearAllTokens() {
    tokenStorage.clearTokens()
    this.isRefreshing = false
    this.refreshPromise = null
    console.log("🔐 All JWT tokens cleared")
  }

  // Refresh access token using refresh token
  private async refreshAccessToken(): Promise<string> {
    const refreshToken = tokenStorage.getRefreshToken()

    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    // If already refreshing, return the existing promise
    if (this.isRefreshing && this.refreshPromise) {
      console.log('🔄 Token refresh already in progress, waiting...')
      return this.refreshPromise
    }

    this.isRefreshing = true
    this.refreshPromise = this.performTokenRefresh(refreshToken)

    try {
      const newAccessToken = await this.refreshPromise
      return newAccessToken
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  // Perform the actual token refresh
  private async performTokenRefresh(refreshToken: string): Promise<string> {
    try {
      console.log('🔄 Attempting token refresh...')
      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Refresh-Token': refreshToken,
        },
        // No body needed since refresh token is in header
      })

      if (!response.ok) {
        console.error(`❌ Token refresh failed with status: ${response.status}`)
        // ANY error during refresh should lead to login redirect
        throw new Error(`Token refresh failed: ${response.status} ${response.statusText}`)
      }

      const data: RefreshTokenResponse = await response.json()
      console.log('✅ Token refresh successful')

      // Update stored tokens (new access token, same or new refresh token)
      tokenStorage.setTokens(data.accessToken, data.refreshToken)

      return data.accessToken
    } catch (error) {
      console.error('❌ Token refresh failed - clearing tokens and redirecting to login:', error)

      // Clear tokens on ANY refresh failure
      this.clearAllTokens()

      // Redirect to login if not already there - for ANY refresh error
      if (typeof window !== "undefined" && window.location.pathname !== "/signin") {
        console.log('🔄 Redirecting to login after ANY refresh failure')
        window.location.href = "/signin"
      }

      // Re-throw the error
      throw error
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.requestWithRetry<T>(endpoint, options, false)
  }

  // Request with automatic token refresh retry
  private async requestWithRetry<T>(endpoint: string, options: RequestInit = {}, isRetry: boolean = false): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`

    // Add Authorization header if we have an access token
    // Skip for auth endpoints to avoid circular dependencies
    const isAuthEndpoint = endpoint.includes('/auth/login') || endpoint.includes('/auth/refresh')
    const accessToken = this.getAccessToken()

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...(accessToken && !isAuthEndpoint && {
          "Authorization": `Bearer ${accessToken}`
        }),
        ...options.headers,
      },
      ...options,
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        // Handle ONLY 403 errors with automatic token refresh (not 401)
        if (response.status === 403 && !isAuthEndpoint && !isRetry) {
          console.log('🔄 403 Forbidden detected - attempting token refresh...')
          try {
            // Try to refresh the token
            await this.refreshAccessToken()
            console.log('✅ Token refresh successful - retrying original request')

            return this.requestWithRetry<T>(endpoint, options, true)
          } catch (refreshError) {
            console.error('❌ Token refresh failed after 403 error - redirecting to login:', refreshError)
            // Clear tokens and redirect to login for ANY refresh error
            this.clearAllTokens()
            if (typeof window !== "undefined" && window.location.pathname !== "/signin") {
              console.log('🔄 Redirecting to login page after refresh failure')
              window.location.href = "/signin"
            }

            throw new Error("Session expired - please login again")
          }
        }

        // Handle 401 errors directly (no refresh attempt)
        if (response.status === 401) {
          console.log('🚫 401 Unauthorized - clearing tokens and redirecting to login')
          this.clearAllTokens()
          if (typeof window !== "undefined" && window.location.pathname !== "/signin") {
            window.location.href = "/signin"
          }
          throw new Error("Unauthorized - please login again")
        }

        // For 403 errors on auth endpoints or retry failures, clear tokens and redirect
        if (response.status === 403 && (isAuthEndpoint || isRetry)) {
          console.log('🚫 403 on auth endpoint or retry failed - clearing tokens and redirecting')
          this.clearAllTokens()
          if (typeof window !== "undefined" && window.location.pathname !== "/signin") {
            window.location.href = "/signin"
          }
        }

        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          errorData.code || response.status.toString(),
        )
      }

      // For DELETE requests, check if there's content to parse
      if (options.method === 'DELETE') {
        const contentLength = response.headers.get('content-length')
        const contentType = response.headers.get('content-type')

        // If no content or content-length is 0, return empty object
        if (contentLength === '0' || !contentType?.includes('application/json')) {
          console.log('🗑️ DELETE request completed - no content to parse')
          return {} as any
        }
      }

      const data = await response.json()

      // Debug logging
      console.log("✅ API Response:", {
        method: options.method || 'GET',
        url: url,
        status: response.status,
        isRetry
      })

      return data
    } catch (error) {
      console.error("❌ API Error:", {
        method: options.method || 'GET',
        url: url,
        error: error instanceof Error ? error.message : error,
        isRetry
      })

      if (error instanceof Error) {
        throw error
      }

      // Network or other errors
      throw new Error(error instanceof Error ? error.message : "An unexpected error occurred")
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    let fullEndpoint = endpoint
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      if (searchParams.toString()) {
        fullEndpoint += `?${searchParams.toString()}`
      }
    }

    return this.request<T>(fullEndpoint, { method: "GET" })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE", body: data ? JSON.stringify(data) : undefined })
  }
}

// Singleton instance
export const apiClient = new ApiClient()
