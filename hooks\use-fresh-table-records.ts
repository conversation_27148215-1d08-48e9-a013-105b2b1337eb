import { useEffect, useRef } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useTableRecords } from '@/lib/queries/records'
import { useAuth } from '@/contexts/auth-context'

/**
 * Custom hook that ensures fresh table records are fetched when navigating to details page
 * This hook automatically clears cache when tableId changes, then lets React Query handle the fetch
 * It also handles authentication errors properly without interfering with 403 refresh logic
 */
export function useFreshTableRecords(
  tableId: string | number,
  params?: { page?: number; size?: number }
) {
  const queryClient = useQueryClient()
  const { isAuthenticated } = useAuth()
  const lastClearedTableId = useRef<string | number | null>(null)

  // Clear cache ONLY when tableId changes (not when params change)
  useEffect(() => {
    if (!tableId || !isAuthenticated) return

    // Only clear cache if we're navigating to a different table
    if (lastClearedTableId.current === tableId) {
      console.log(`⏭️ [useFreshTableRecords] Same table ${tableId} - skipping cache clear`)
      return
    }

    console.log(`🧹 [useFreshTableRecords] Clearing cache for NEW table ${tableId}`)

    // Clear only records cache for this specific table to avoid affecting other tables
    queryClient.removeQueries({
      queryKey: ['records', 'list'],
      exact: false
    })

    // Remember that we cleared cache for this table
    lastClearedTableId.current = tableId

    console.log(`✅ [useFreshTableRecords] Cache cleared for table ${tableId}`)

  }, [tableId, queryClient, isAuthenticated])

  // Use the standard table records hook - it will automatically fetch since cache is cleared
  // The hook will handle 403 errors with token refresh automatically via ApiClient
  const query = useTableRecords(tableId, params)

  // Don't return data if user is not authenticated to prevent stale data display
  if (!isAuthenticated) {
    return {
      ...query,
      data: undefined,
      isLoading: false,
      error: new Error('Not authenticated')
    }
  }

  return query
}
