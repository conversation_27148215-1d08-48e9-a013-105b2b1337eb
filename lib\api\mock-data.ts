import type { ApiResponse, LoginResponse, User, CoffresResponse, Coffre, RecordsResponse, Record } from "./types"

// Mock Users
const mockUsers: User[] = [
  {
    id: "1",
    name: "Adil",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=40&width=40",
    role: "admin",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "user",
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-14T15:20:00Z",
  },
]

// Mock Coffres
const mockCoffres: Coffre[] = [
  {
    id: 1,
    name: "الكوفر الكبير",
    nameKey: "home.category.a",
    createdDate: "2024-01-10",
    lastUpdated: "2024-01-15",
    recordsCount: 32,
    initialValue: 1000,
    actualValue: 700,
    classification: "Core",
    gradient: "bg-blue-500",
    textColor: "text-black",
    status: "active",
    userId: "1",
  },
  {
    id: 2,
    name: "الكوفر عادل",
    nameKey: "home.category.b",
    createdDate: "2024-01-05",
    lastUpdated: "2024-01-14",
    recordsCount: 45,
    initialValue: 2500,
    actualValue: 1800,
    classification: "Premium",
    gradient: "bg-purple-500",
    textColor: "text-black",
    status: "active",
    userId: "1",
  },
  {
    id: 3,
    name: "الكوفر خليط",
    nameKey: "home.category.c",
    createdDate: "2024-01-08",
    lastUpdated: "2024-01-12",
    recordsCount: 12,
    initialValue: 800,
    actualValue: 650,
    classification: "Standard",
    gradient: "bg-green-500",
    textColor: "text-black",
    status: "active",
    userId: "1",
  },
  {
    id: 4,
    name: "الكوفر كريدي",
    nameKey: "home.category.d",
    createdDate: "2024-01-03",
    lastUpdated: "2024-01-11",
    recordsCount: 28,
    initialValue: 1500,
    actualValue: 950,
    classification: "Advanced",
    gradient: "bg-yellow-500",
    textColor: "text-black",
    status: "active",
    userId: "1",
  },
]

// Mock Records
const mockRecords: Record[] = [
  {
    id: "REC-001",
    coffreId: 1,
    nameKey: "record.transaction.alpha",
    value: 150,
    date: "2024-01-15",
    time: "10:30 AM",
    commentKey: "comment.monthly.subscription",
    status: "completed",
    priority: "high",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: "REC-002",
    coffreId: 1,
    nameKey: "record.payment.beta",
    value: 75,
    date: "2024-01-14",
    time: "02:15 PM",
    commentKey: "comment.office.supplies",
    status: "pending",
    priority: "medium",
    createdAt: "2024-01-14T14:15:00Z",
    updatedAt: "2024-01-14T14:15:00Z",
  },
  // Add more mock records as needed...
]

// Mock API Responses
export const mockApiResponses = {
  // Auth endpoints
  "POST /auth/login": (email: string, password: string): ApiResponse<LoginResponse> => {
    console.log("Mock login called with:", { email, password })

    // Simulate authentication logic
    const user = mockUsers.find((u) => u.email === email)

    if (!user || password !== "password") {
      console.log("Mock login failed - invalid credentials")
      throw new Error("Invalid credentials")
    }

    const response = {
      data: {
        user,
        token: "mock-jwt-token-" + Date.now(),
        refreshToken: "mock-refresh-token-" + Date.now(),
        expiresIn: 3600,
      },
      message: "Login successful",
      success: true,
      timestamp: new Date().toISOString(),
    }

    console.log("Mock login successful:", response)
    return response
  },

  "POST /auth/logout": (): ApiResponse<null> => {
    console.log("Mock logout called")
    return {
      data: null,
      message: "Logout successful",
      success: true,
      timestamp: new Date().toISOString(),
    }
  },

  "GET /auth/me": (userId: string): ApiResponse<User> => {
    console.log("Mock getCurrentUser called with userId:", userId)

    const user = mockUsers.find((u) => u.id === userId)
    if (!user) {
      console.log("Mock getCurrentUser failed - user not found")
      throw new Error("User not found")
    }

    const response = {
      data: user,
      message: "User profile retrieved",
      success: true,
      timestamp: new Date().toISOString(),
    }

    console.log("Mock getCurrentUser successful:", response)
    return response
  },

  // Coffres endpoints
  "GET /coffres": (params?: { page?: number; limit?: number; userId?: string }): ApiResponse<CoffresResponse> => {
    const { page = 1, limit = 10, userId } = params || {}
    let filteredCoffres = mockCoffres

    if (userId) {
      filteredCoffres = mockCoffres.filter((c) => c.userId === userId)
    }

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedCoffres = filteredCoffres.slice(startIndex, endIndex)

    return {
      data: {
        coffres: paginatedCoffres,
        total: filteredCoffres.length,
        page,
        limit,
      },
      message: "Coffres retrieved successfully",
      success: true,
      timestamp: new Date().toISOString(),
    }
  },

  "GET /coffres/:id": (id: number): ApiResponse<Coffre> => {
    const coffre = mockCoffres.find((c) => c.id === id)
    if (!coffre) {
      throw new Error("Coffre not found")
    }

    return {
      data: coffre,
      message: "Coffre retrieved successfully",
      success: true,
      timestamp: new Date().toISOString(),
    }
  },

  // Records endpoints
  "GET /coffres/:id/records": (
    coffreId: number,
    params?: { page?: number; limit?: number },
  ): ApiResponse<RecordsResponse> => {
    const { page = 1, limit = 10 } = params || {}
    const filteredRecords = mockRecords.filter((r) => r.coffreId === coffreId)

    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedRecords = filteredRecords.slice(startIndex, endIndex)

    return {
      data: {
        records: paginatedRecords,
        total: filteredRecords.length,
        page,
        limit,
      },
      message: "Records retrieved successfully",
      success: true,
      timestamp: new Date().toISOString(),
    }
  },
}

// Mock delay function for realistic API simulation
export const mockDelay = (ms = 500) => new Promise((resolve) => setTimeout(resolve, ms))
