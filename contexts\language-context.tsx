"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { DirectionProvider } from "@radix-ui/react-direction"
import { safeLocalStorage } from "@/lib/utils/storage"

export type Language = "en" | "fr" | "ar"

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
  isRTL: boolean
  direction: "ltr" | "rtl"
  formatCurrency: (amount: number) => string
  formatNumber: (num: number) => string
  formatDate: (dateString: string) => string
  toArabicNumerals: (text: string) => string
  formatCurrencyFrench: (amount: number) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Translation data
const translations = {
  en: {
    // Header
    "app.title": "Adil's Coffres",
    "header.notifications": "Notifications",
    "header.profile": "Profile",
    "header.settings": "Settings",

    // Home Page
    "home.search.placeholder": "Search...",
    "home.category.a": "الكوفر الكبير",
    "home.category.b": "الكوفر عادل",
    "home.category.c": "الكوفر خليط",
    "home.category.d": "الكوفر كريدي",
    "home.initial": "Initial",
    "home.records": "Records",
    "home.remaining": "Remaining",
    "home.hide.prices": "Hide Prices",
    "home.show.prices": "Show Prices",

    // Search Page
    "search.title": "Search",
    "search.placeholder": "Type to search...",
    "search.recent": "Recent Searches",
    "search.suggested": "Suggested Keywords",
    "search.results": "Results for",
    "search.no.results": "Search functionality would be implemented here",

    // Details Page
    "details.initial.value": "Initial Value",
    "details.records.count": "Records Count",
    "details.total.deducted": "Total Deducted",
    "details.remaining": "Remaining",
    "details.transaction.records": "Transaction Records",
    "details.date": "Date",
    "details.value": "Value",
    "details.comment": "Comment",
    "details.not.found": "Container Not Found",
    "details.back.home": "Back to Home",
    "details.at": "at",
    "details.consumed": "Consumed",
    "details.add.record": "Add Record",
    "details.initial": "الأولي",
    "details.current": "الحالي",
    "details.records": "السجلات",
    "details.usage.ratio": "نسبة الاستخدام",
    "details.created": "تم الإنشاء",
    "details.updated": "آخر تحديث",

    // Pagination
    "pagination.items.per.page": "Items per page",
    "pagination.showing": "Showing",
    "pagination.of": "of",
    "pagination.previous": "Previous",
    "pagination.next": "Next",

    // Records
    "record.transaction.alpha": "Transaction Alpha",
    "record.payment.beta": "Payment Beta",
    "record.transfer.gamma": "Transfer Gamma",
    "record.purchase.delta": "Purchase Delta",
    "record.service.fee": "Service Fee",
    "record.monthly.subscription": "Monthly Subscription",
    "record.equipment.rental": "Equipment Rental",
    "record.maintenance.cost": "Maintenance Cost",
    "record.office.supplies": "Office Supplies",
    "record.software.license": "Software License",
    "record.training.materials": "Training Materials",
    "record.marketing.campaign": "Marketing Campaign",
    "record.advertisement": "Advertisement",
    "record.promotional.items": "Promotional Items",
    "record.event.sponsorship": "Event Sponsorship",

    // Comments
    "comment.monthly.subscription": "Monthly subscription payment for premium services",
    "comment.office.supplies": "Office supplies and equipment purchase",
    "comment.software.license": "Software license renewal fee",
    "comment.marketing.campaign": "Marketing campaign expenses",
    "comment.professional.consulting": "Professional consulting services",
    "comment.cloud.storage": "Cloud storage and backup services",
    "comment.conference.room": "Conference room equipment rental",
    "comment.system.maintenance": "Regular system maintenance and updates",
    "comment.stationery": "Stationery and printing materials",
    "comment.design.software": "Design software annual license",
    "comment.employee.training": "Employee training and development resources",
    "comment.social.media": "Social media advertising campaign",
    "comment.online.banner": "Online banner advertisements",
    "comment.branded.merchandise": "Branded merchandise for events",
    "comment.community.event": "Local community event sponsorship",

    // Language
    "language.english": "English",
    "language.french": "Français",
    "language.arabic": "العربية",

    // Profile & Settings
    "profile.title": "Profile",
    "profile.name": "Full Name",
    "profile.email": "Email Address",
    "profile.phone": "Phone Number",
    "profile.save": "Save Changes",
    "settings.title": "Settings",
    "settings.language": "Language",
    "settings.notifications": "Notifications",
    "settings.theme": "Theme",
    "settings.privacy": "Privacy",

    // Actions
    "actions.choose": "Choose an action",
    "actions.edit": "Edit",
    "actions.delete": "Delete",
    "actions.cancel": "Cancel",
  },
  fr: {
    // Header
    "app.title": "Les Coffres d'Adil",
    "header.notifications": "Notifications",
    "header.profile": "Profil",
    "header.settings": "Paramètres",

    // Home Page
    "home.search.placeholder": "Rechercher...",
    "home.category.a": "الكوفر الكبير",
    "home.category.b": "الكوفر عادل",
    "home.category.c": "الكوفر خليط",
    "home.category.d": "الكوفر كريدي",
    "home.initial": "Initial",
    "home.records": "Enregistrements",
    "home.remaining": "Restant",
    "home.hide.prices": "Masquer les Prix",
    "home.show.prices": "Afficher les Prix",

    // Search Page
    "search.title": "Recherche",
    "search.placeholder": "Tapez pour rechercher...",
    "search.recent": "Recherches Récentes",
    "search.suggested": "Mots-clés Suggérés",
    "search.results": "Résultats pour",
    "search.no.results": "La fonctionnalité de recherche serait implémentée ici",

    // Details Page
    "details.initial.value": "Valeur Initiale",
    "details.records.count": "Nombre d'Enregistrements",
    "details.total.deducted": "Total Déduit",
    "details.remaining": "Restant",
    "details.transaction.records": "Enregistrements de Transaction",
    "details.date": "Date",
    "details.value": "Valeur",
    "details.comment": "Commentaire",
    "details.not.found": "Conteneur Non Trouvé",
    "details.back.home": "Retour à l'Accueil",
    "details.at": "à",
    "details.consumed": "Consommé",
    "details.add.record": "Ajouter un Enregistrement",
    "details.initial": "الأولي",
    "details.current": "الحالي",
    "details.records": "السجلات",
    "details.usage.ratio": "نسبة الاستخدام",
    "details.created": "تم الإنشاء",
    "details.updated": "آخر تحديث",

    // Pagination
    "pagination.items.per.page": "Éléments par page",
    "pagination.showing": "Affichage",
    "pagination.of": "de",
    "pagination.previous": "Précédent",
    "pagination.next": "Suivant",

    // Table Headers (Arabic)
    "table.name": "الاسم",
    "table.amount": "المبلغ",
    "table.date.out": "تاريخ الخروج",
    "table.date.return": "تاريخ الإرجاع",

    // Records
    "record.transaction.alpha": "Transaction Alpha",
    "record.payment.beta": "Paiement Beta",
    "record.transfer.gamma": "Transfert Gamma",
    "record.purchase.delta": "Achat Delta",
    "record.service.fee": "Frais de Service",
    "record.monthly.subscription": "Abonnement Mensuel",
    "record.equipment.rental": "Location d'Équipement",
    "record.maintenance.cost": "Coût de Maintenance",
    "record.office.supplies": "Fournitures de Bureau",
    "record.software.license": "Licence Logiciel",
    "record.training.materials": "Matériel de Formation",
    "record.marketing.campaign": "Campagne Marketing",
    "record.advertisement": "Publicité",
    "record.promotional.items": "Articles Promotionnels",
    "record.event.sponsorship": "Parrainage d'Événement",

    // Comments
    "comment.monthly.subscription": "Paiement d'abonnement mensuel pour les services premium",
    "comment.office.supplies": "Achat de fournitures et équipements de bureau",
    "comment.software.license": "Frais de renouvellement de licence logiciel",
    "comment.marketing.campaign": "Dépenses de campagne marketing",
    "comment.professional.consulting": "Services de conseil professionnel",
    "comment.cloud.storage": "Services de stockage cloud et de sauvegarde",
    "comment.conference.room": "Location d'équipement de salle de conférence",
    "comment.system.maintenance": "Maintenance et mises à jour régulières du système",
    "comment.stationery": "Papeterie et matériel d'impression",
    "comment.design.software": "Licence annuelle de logiciel de design",
    "comment.employee.training": "Ressources de formation et développement des employés",
    "comment.social.media": "Campagne publicitaire sur les réseaux sociaux",
    "comment.online.banner": "Publicités bannières en ligne",
    "comment.branded.merchandise": "Marchandise de marque pour les événements",
    "comment.community.event": "Parrainage d'événement communautaire local",

    // Language
    "language.english": "English",
    "language.french": "Français",
    "language.arabic": "العربية",

    // Profile & Settings
    "profile.title": "Profil",
    "profile.name": "Nom Complet",
    "profile.email": "Adresse Email",
    "profile.phone": "Numéro de Téléphone",
    "profile.save": "Sauvegarder",
    "settings.title": "Paramètres",
    "settings.language": "Langue",
    "settings.notifications": "Notifications",
    "settings.theme": "Thème",
    "settings.privacy": "Confidentialité",

    // Actions
    "actions.choose": "Choisir une action",
    "actions.edit": "Modifier",
    "actions.delete": "Supprimer",
    "actions.cancel": "Annuler",
  },
  ar: {
    // Header
    "app.title": "خزائن عادل",
    "header.notifications": "الإشعارات",
    "header.profile": "الملف الشخصي",
    "header.settings": "الإعدادات",

    // Home Page
    "home.search.placeholder": "بحث...",
    "home.category.a": "الكوفر الكبير",
    "home.category.b": "الكوفر عادل",
    "home.category.c": "الكوفر خليط",
    "home.category.d": "الكوفر كريدي",
    "home.initial": "الأولي",
    "home.records": "السجلات",
    "home.remaining": "المتبقي",
    "home.hide.prices": "إخفاء الأسعار",
    "home.show.prices": "إظهار الأسعار",

    // Search Page
    "search.title": "البحث",
    "search.placeholder": "اكتب للبحث...",
    "search.recent": "عمليات البحث الأخيرة",
    "search.suggested": "الكلمات المقترحة",
    "search.results": "النتائج لـ",
    "search.no.results": "سيتم تنفيذ وظيفة البحث هنا",

    // Details Page
    "details.initial.value": "القيمة الأولية",
    "details.records.count": "عدد السجلات",
    "details.total.deducted": "إجمالي المخصوم",
    "details.remaining": "المتبقي",
    "details.transaction.records": "سجلات المعاملات",
    "details.date": "التاريخ",
    "details.value": "القيمة",
    "details.comment": "التعليق",
    "details.not.found": "الحاوية غير موجودة",
    "details.back.home": "العودة للرئيسية",
    "details.at": "في",
    "details.consumed": "المستهلك",
    "details.add.record": "إضافة سجل",
    "details.initial": "الأولي",
    "details.current": "الحالي",
    "details.records": "السجلات",
    "details.usage.ratio": "نسبة الاستخدام",
    "details.created": "تم الإنشاء",
    "details.updated": "آخر تحديث",

    // Pagination
    "pagination.items.per.page": "عناصر في الصفحة",
    "pagination.showing": "عرض",
    "pagination.of": "من",
    "pagination.previous": "السابق",
    "pagination.next": "التالي",

    // Records
    "record.transaction.alpha": "المعاملة ألفا",
    "record.payment.beta": "الدفع بيتا",
    "record.transfer.gamma": "التحويل جاما",
    "record.purchase.delta": "الشراء دلتا",
    "record.service.fee": "رسوم الخدمة",
    "record.monthly.subscription": "الاشتراك الشهري",
    "record.equipment.rental": "تأجير المعدات",
    "record.maintenance.cost": "تكلفة الصيانة",
    "record.office.supplies": "مستلزمات المكتب",
    "record.software.license": "رخصة البرنامج",
    "record.training.materials": "مواد التدريب",
    "record.marketing.campaign": "حملة التسويق",
    "record.advertisement": "الإعلان",
    "record.promotional.items": "العناصر الترويجية",
    "record.event.sponsorship": "رعاية الحدث",

    // Comments
    "comment.monthly.subscription": "دفع الاشتراك الشهري للخدمات المميزة",
    "comment.office.supplies": "شراء مستلزمات ومعدات المكتب",
    "comment.software.license": "رسوم تجديد رخصة البرنامج",
    "comment.marketing.campaign": "مصاريف الحملة التسويقية",
    "comment.professional.consulting": "خدمات الاستشارات المهنية",
    "comment.cloud.storage": "خدمات التخزين السحابي والنسخ الاحتياطي",
    "comment.conference.room": "تأجير معدات قاعة المؤتمرات",
    "comment.system.maintenance": "صيانة وتحديثات النظام المنتظمة",
    "comment.stationery": "القرطاسية ومواد الطباعة",
    "comment.design.software": "رخصة برنامج التصميم السنوية",
    "comment.employee.training": "موارد تدريب وتطوير الموظفين",
    "comment.social.media": "حملة إعلانية على وسائل التواصل الاجتماعي",
    "comment.online.banner": "إعلانات البانر عبر الإنترنت",
    "comment.branded.merchandise": "البضائع ذات العلامة التجارية للأحداث",
    "comment.community.event": "رعاية حدث مجتمعي محلي",

    // Language
    "language.english": "English",
    "language.french": "Français",
    "language.arabic": "العربية",

    // Profile & Settings
    "profile.title": "الملف الشخصي",
    "profile.name": "الاسم الكامل",
    "profile.email": "عنوان البريد الإلكتروني",
    "profile.phone": "رقم الهاتف",
    "profile.save": "حفظ التغييرات",
    "settings.title": "الإعدادات",
    "settings.language": "اللغة",
    "settings.notifications": "الإشعارات",
    "settings.theme": "المظهر",
    "settings.privacy": "الخصوصية",

    // Actions
    "actions.choose": "اختر إجراءً",
    "actions.edit": "تعديل",
    "actions.delete": "حذف",
    "actions.cancel": "إلغاء",
  },
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>("fr") // Force French as default
  const [isClient, setIsClient] = useState(false)

  // Set client flag
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Force French language - no localStorage loading
  useEffect(() => {
    if (!isClient) return
    setLanguage("fr") // Always force French
  }, [isClient])

  // Force French language and LTR direction
  useEffect(() => {
    if (!isClient) return

    // Always force French
    safeLocalStorage.setItem("language", "fr")

    // Update document language and direction - always LTR for French
    if (typeof document !== "undefined") {
      document.documentElement.lang = "fr"
      document.documentElement.dir = "ltr"
    }
  }, [isClient])

  const t = (key: string): string => {
    return translations[language][key as keyof (typeof translations)[typeof language]] || key
  }

  const isRTL = false // Always LTR for French
  const direction = "ltr" // Always LTR

  // Always use French formatting for numbers and dates, no currency suffix
  const formatCurrency = (amount: number): string => {
    return amount.toLocaleString("fr-FR")
  }

  const formatNumber = (num: number): string => {
    return num.toLocaleString("fr-FR")
  }

  const formatDate = (dateString: string): string => {
    // Handle null, undefined, empty string, or invalid dates
    if (!dateString || dateString.trim() === "") {
      return "-"
    }

    const date = new Date(dateString)

    // Check if the date is invalid
    if (isNaN(date.getTime())) {
      return "-"
    }

    const formatted = date.toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    // Replace "/" with "-" for dd-mm-yyyy format
    return formatted.replace(/\//g, "-")
  }

  const toArabicNumerals = (text: string): string => {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩']
    return text.replace(/[0-9]/g, (digit) => arabicNumerals[parseInt(digit)])
  }

  const formatCurrencyFrench = (amount: number): string => {
    // Ensure we have a valid number
    const numAmount = Number(amount) || 0

    // Manual formatting with spaces as thousands separators
    const numStr = Math.abs(numAmount).toString()
    const formatted = numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ' ')

    // Add negative sign if needed
    const result = numAmount < 0 ? '-' + formatted : formatted

    return result + " MAD"
  }

  return (
    <DirectionProvider dir={direction}>
      <LanguageContext.Provider
        value={{
          language,
          setLanguage,
          t,
          isRTL,
          direction,
          formatCurrency,
          formatNumber,
          formatDate,
          toArabicNumerals,
          formatCurrencyFrench,
        }}
      >
        {children}
      </LanguageContext.Provider>
    </DirectionProvider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
