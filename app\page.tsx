"use client"

import { useState } from "react"
import { <PERSON>, <PERSON>O<PERSON>, Refresh<PERSON><PERSON>, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader } from "@/components/ui/card"

import { UserMenu } from "@/components/user-menu"
import { useLanguage } from "@/contexts/language-context"
import { useDirection } from "@radix-ui/react-direction"
import { useFreshTables } from "@/hooks/use-fresh-tables"
import Link from "next/link"
import { Table } from "@/lib/api-client-types"

export default function HomePage() {
  const [visibleContent, setVisibleContent] = useState<Set<number>>(new Set())

  const { t, isRTL, formatDate } = useLanguage()
  const dir = useDirection()

  // Fetch only tables list - no rest amounts or individual table details
  const {
    coffres,
    isLoading,
    error,
    refetch,
  } = useFreshTables()



  const toggleContentVisibility = (coffreId: number) => {
    const newVisibleContent = new Set(visibleContent)
    if (newVisibleContent.has(coffreId)) {
      newVisibleContent.delete(coffreId) // Hide content
    } else {
      newVisibleContent.add(coffreId) // Show content
    }
    setVisibleContent(newVisibleContent)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#0A66C2] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement de l'application...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Something went wrong</h2>
          <p className="text-gray-600 mb-4">{(error as any).message || "Failed to load coffres"}</p>
          <Button onClick={() => refetch()} className="bg-[#0A66C2] hover:bg-[#0A66C2]/90">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    )
  }
 


  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100" dir={dir}>
      {/* Sticky Header */}
      <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className={`flex items-center ${isRTL ? "flex-row-reverse space-x-reverse" : ""} space-x-3`}>
            <div className="w-10 h-10">
              <img
                src="/app_logo.png"
                alt="App Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <div className={isRTL ? "text-right" : "text-left"}>
              <h1 className="text-xl font-bold text-gray-900">{t("app.title")}</h1>
            </div>
          </div>

          <div className={`flex items-center ${isRTL ? "flex-row-reverse space-x-reverse" : ""} space-x-3`}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
              className="hover:bg-gray-100"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <UserMenu />
          </div>
        </div>
      </header>      

      <main className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {coffres?.map((coffre : Table) => {
            const isContentHidden = !visibleContent.has(coffre.id) // Content is hidden by default

            return (
              <div key={coffre.id} className="group">
                <Link
                  href={`/details/${coffre.id}?tableData=${encodeURIComponent(JSON.stringify({
                    id: coffre.id,
                    coffreName: coffre.coffreName,
                    coffreNameIhm: coffre.coffreNameIhm,
                    coffreTotal: coffre.coffreTotal,
                    dateDerniereModification: coffre.dateDerniereModification,
                    coffreColor: coffre.coffreColor
                  }))}`}
                >
                  <Card className="hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 border-0 overflow-hidden cursor-pointer">
                    <CardHeader
                      className={`p-6 relative overflow-hidden`}
                      style={{ backgroundColor: coffre.coffreColor }}
                    >
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              toggleContentVisibility(coffre.id)
                            }}
                            className="p-1 hover:bg-white/20"
                          >
                            {isContentHidden ? <EyeOff className="h-4 w-4 text-black" /> : <Eye className="h-4 w-4 text-black" />}
                          </Button>

                          <h3
                            className="text-xl text-right text-black"
                            dir="rtl"
                            style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.5)' }}
                          >
                            {coffre.coffreNameIhm}
                          </h3>
                        </div>

                      {/* Rest Amount with Attractive Red Styling */}
                      <div className="bg-gradient-to-br from-red-500/90 to-red-600/90 rounded-xl p-4 backdrop-blur-sm border border-red-400/50 shadow-lg">
                        <div className="text-center">
                          {/* Last Update - Red Background like Screenshot */}
                          <div className="bg-red-500 rounded-lg px-4 py-2 shadow-lg">
                            <div className="flex items-center justify-center space-x-2 space-x-reverse" dir="rtl">
                              <span className="font-bold text-white text-sm date-french">{formatDate(coffre.dateDerniereModification)}</span>
                              <span className="text-white font-medium text-sm">آخر تحديث</span>
                              <Clock className="h-4 w-4 text-white" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                </Card>
                </Link>
              </div>
            )
          })}
        </div>
      </main>
    </div>
  )
}
