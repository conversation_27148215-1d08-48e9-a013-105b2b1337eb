import { ApiClient } from "@/lib/api-client"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import type { UpdateTableRequest } from "@/lib/api-client-types"
import toast from 'react-hot-toast'

// Query Keys
export const tablesKeys = {
  all: ["tables"] as const,
  lists: () => [...tablesKeys.all, "list"] as const,
  list: (filters?: Record<string, any>) => [...tablesKeys.lists(), { filters }] as const,
  details: () => [...tablesKeys.all, "detail"] as const,
  detail: (id: number) => [...tablesKeys.details(), id] as const,
}

// Query hook for fetching all tables (coffres)
export function useCoffres() {
  return useQuery({
    queryKey: tablesKeys.list(),
    queryFn: async () => {
      try {
        console.log('🏠 [useCoffres] Fetching fresh tables list')
        const result = await ApiClient.getTables()
        console.log(`✅ [useCoffres] Fetched ${result?.length || 0} tables`)
        return result
      } catch (error) {
        console.error('❌ [useCoffres] Failed to fetch tables:', error)
        throw error
      }
    },
    staleTime: 0, // ALWAYS fetch fresh data when cache is cleared
    gcTime: 1 * 60 * 1000, // Keep in cache for only 1 minute after component unmounts
    refetchOnMount: false, // Don't auto-refetch - cache clearing will trigger fetch
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
  })
}

// Mutation hook for updating table
export function useUpdateTable() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTableRequest }) =>
      ApiClient.updateTable(id, data),
    onSuccess: (data, variables) => {
      console.log(`✅ Table ${variables.id} updated successfully:`, data)
      toast.success('Table mise à jour avec succès!')

      // Invalidate table queries to refresh data
      queryClient.invalidateQueries({ queryKey: tablesKeys.detail(variables.id) })
      queryClient.invalidateQueries({ queryKey: tablesKeys.lists() })
      queryClient.invalidateQueries({ queryKey: ['table', variables.id.toString()] })

      // Also invalidate records queries to refresh financial data (coffreTotal, etc.)
      queryClient.invalidateQueries({ queryKey: ['records', variables.id.toString()] })

      console.log(`🔄 Invalidated queries for table ${variables.id} and its records`)
    },
    onError: (error) => {
      console.error(`❌ Failed to update table:`, error)
      toast.error('Échec de la mise à jour de la table')
    },
  })
}