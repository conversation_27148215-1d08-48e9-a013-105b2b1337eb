"use client"

import type React from "react"

import { useLanguage } from "@/contexts/language-context"
import { cn } from "@/lib/utils"

interface RTLWrapperProps {
  children: React.ReactNode
  className?: string
}

export function RTLWrapper({ children, className }: RTLWrapperProps) {
  const { isRTL, dir } = useLanguage()

  return (
    <div dir={dir} className={cn("w-full", isRTL && "rtl-container", className)}>
      {children}
    </div>
  )
}
