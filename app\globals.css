@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced RTL Support */
[dir="rtl"] {
  font-family: "Segoe UI", "Tahoma", "Arial", "Helvetica Neue", sans-serif;
}

/* Force RTL layout for all flex containers */
[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-col {
  flex-direction: column;
}

/* RTL Grid support */
[dir="rtl"] .grid {
  direction: rtl;
}

/* RTL Text alignment */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-right {
  text-align: left !important;
}

[dir="rtl"] .text-center {
  text-align: center !important;
}

/* RTL Spacing */
[dir="rtl"] .space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

/* RTL Margins */
[dir="rtl"] .mr-1 {
  margin-right: 0;
  margin-left: 0.25rem;
}
[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}
[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}
[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}
[dir="rtl"] .ml-1 {
  margin-left: 0;
  margin-right: 0.25rem;
}
[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}
[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}
[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

/* RTL Padding */
[dir="rtl"] .pr-1 {
  padding-right: 0;
  padding-left: 0.25rem;
}
[dir="rtl"] .pr-2 {
  padding-right: 0;
  padding-left: 0.5rem;
}
[dir="rtl"] .pr-3 {
  padding-right: 0;
  padding-left: 0.75rem;
}
[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}
[dir="rtl"] .pl-1 {
  padding-left: 0;
  padding-right: 0.25rem;
}
[dir="rtl"] .pl-2 {
  padding-left: 0;
  padding-right: 0.5rem;
}
[dir="rtl"] .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}
[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

/* RTL Positioning */
[dir="rtl"] .left-0 {
  left: auto;
  right: 0;
}
[dir="rtl"] .right-0 {
  right: auto;
  left: 0;
}
[dir="rtl"] .-left-1 {
  left: auto;
  right: -0.25rem;
}
[dir="rtl"] .-right-1 {
  right: auto;
  left: -0.25rem;
}

/* RTL Justify content */
[dir="rtl"] .justify-start {
  justify-content: flex-end;
}
[dir="rtl"] .justify-end {
  justify-content: flex-start;
}
[dir="rtl"] .justify-between {
  justify-content: space-between;
}

/* RTL Items alignment */
[dir="rtl"] .items-start {
  align-items: flex-start;
}
[dir="rtl"] .items-end {
  align-items: flex-end;
}

/* Input RTL support */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="search"],
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="password"],
[dir="rtl"] input[type="number"],
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
  direction: rtl;
}

[dir="rtl"] input::placeholder,
[dir="rtl"] textarea::placeholder {
  text-align: right;
}

/* Force numbers and dates to use French formatting */
.number-french,
.date-french,
.currency-french {
  direction: ltr;
  font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
}

/* RTL specific overrides */
[dir="rtl"] .number-french,
[dir="rtl"] .date-french,
[dir="rtl"] .currency-french {
  direction: ltr;
  text-align: left;
  font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
}

/* Button RTL support */
[dir="rtl"] .btn-rtl {
  flex-direction: row-reverse;
}

/* Card RTL support */
[dir="rtl"] .card-rtl {
  direction: rtl;
}

[dir="rtl"] .card-rtl .flex {
  flex-direction: row-reverse;
}

/* Badge positioning for RTL */
[dir="rtl"] .badge-rtl {
  left: -0.25rem;
  right: auto;
}

/* Dropdown RTL support */
[dir="rtl"] .dropdown-rtl {
  left: 0;
  right: auto;
}
