"use client"

import { useState, useEffect } from "react"
import { trackAuthEvent, ANALYTICS_ACTIONS } from "./analytics"

// Safe localStorage wrapper for SSR compatibility
export const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (typeof window === "undefined") return null
    try {
      return localStorage.getItem(key)
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return null
    }
  },

  setItem: (key: string, value: string): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  },

  removeItem: (key: string): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error)
    }
  },

  clear: (): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.clear()
    } catch (error) {
      console.warn("Error clearing localStorage:", error)
    }
  },
}

// Safe sessionStorage wrapper
export const safeSessionStorage = {
  getItem: (key: string): string | null => {
    if (typeof window === "undefined") return null
    try {
      return sessionStorage.getItem(key)
    } catch (error) {
      console.warn(`Error reading sessionStorage key "${key}":`, error)
      return null
    }
  },

  setItem: (key: string, value: string): void => {
    if (typeof window === "undefined") return
    try {
      sessionStorage.setItem(key, value)
    } catch (error) {
      console.warn(`Error setting sessionStorage key "${key}":`, error)
    }
  },

  removeItem: (key: string): void => {
    if (typeof window === "undefined") return
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.warn(`Error removing sessionStorage key "${key}":`, error)
    }
  },

  clear: (): void => {
    if (typeof window === "undefined") return
    try {
      sessionStorage.clear()
    } catch (error) {
      console.warn("Error clearing sessionStorage:", error)
    }
  },
}

// JWT Token Storage Keys
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'auth_access_token',
  REFRESH_TOKEN: 'auth_refresh_token',
} as const

// JWT Token Storage Utilities
export const tokenStorage = {
  // Get access token
  getAccessToken: (): string | null => {
    return safeLocalStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN)
  },

  // Get refresh token
  getRefreshToken: (): string | null => {
    return safeLocalStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN)
  },

  // Set both tokens
  setTokens: (accessToken: string, refreshToken: string): void => {
    safeLocalStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken)
    safeLocalStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, refreshToken)
    console.log('🔐 Tokens stored in localStorage')
  },

  // Update only access token (for refresh scenarios)
  setAccessToken: (accessToken: string): void => {
    safeLocalStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken)
    console.log('🔐 Access token updated in localStorage')
  },

  // Clear all tokens
  clearTokens: (): void => {
    safeLocalStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN)
    safeLocalStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN)
    console.log('🔐 All tokens cleared from localStorage')
  },

  // Check if user has valid tokens
  hasValidTokens: (): boolean => {
    const accessToken = tokenStorage.getAccessToken()
    const refreshToken = tokenStorage.getRefreshToken()
    return !!(accessToken && refreshToken)
  },

  // Check if user has refresh token (for token refresh scenarios)
  hasRefreshToken: (): boolean => {
    return !!tokenStorage.getRefreshToken()
  }
}



// Setup token cleanup on browser/app close and inactivity
export const setupTokenCleanup = () => {
  if (typeof window === "undefined") return

  // Check if this is a new browser session
  const SESSION_KEY = 'app_session_active'
  const wasSessionActive = sessionStorage.getItem(SESSION_KEY)

  if (!wasSessionActive) {
    // New browser session - clear any existing tokens
    console.log('🔐 New browser session detected - clearing tokens')
    tokenStorage.clearTokens()
  }

  // Mark session as active
  sessionStorage.setItem(SESSION_KEY, 'true')

  // Inactivity timer - 30 seconds
  let inactivityTimer: NodeJS.Timeout | null = null
  const INACTIVITY_TIMEOUT = 30 * 1000 // 30 seconds

  const clearTokensAndRedirect = () => {
    console.log('🔐 Inactivity timeout or app closing - clearing tokens')

    // Track session timeout
    trackAuthEvent(ANALYTICS_ACTIONS.SESSION_TIMEOUT, true, {
      reason: 'inactivity_timeout',
      timeout_duration: INACTIVITY_TIMEOUT
    })

    tokenStorage.clearTokens()
    sessionStorage.removeItem(SESSION_KEY)

    // Redirect to signin if still on the page
    if (typeof window !== "undefined" && window.location.pathname !== "/signin") {
      window.location.href = "/signin"
    }
  }

  const resetInactivityTimer = () => {
    if (inactivityTimer) {
      clearTimeout(inactivityTimer)
    }

    // Only set timer if user is authenticated (has tokens)
    if (tokenStorage.hasValidTokens()) {
      inactivityTimer = setTimeout(() => {
        console.log('🔐 30 seconds of inactivity - clearing tokens')
        clearTokensAndRedirect()
      }, INACTIVITY_TIMEOUT)
    }
  }

  const handleVisibilityChange = () => {
    if (document.visibilityState === 'hidden') {
      // Tab became inactive - start inactivity timer
      console.log('🔐 Tab inactive - starting 30s timer')
      resetInactivityTimer()
    } else if (document.visibilityState === 'visible') {
      // Tab became active - clear timer
      console.log('🔐 Tab active - clearing timer')
      if (inactivityTimer) {
        clearTimeout(inactivityTimer)
        inactivityTimer = null
      }
    }
  }

  const handleBeforeUnload = () => {
    console.log('🔐 Browser/App closing - clearing all tokens')
    clearTokensAndRedirect()
  }

  // Setup event listeners
  document.addEventListener('visibilitychange', handleVisibilityChange)
  window.addEventListener('beforeunload', handleBeforeUnload)
  window.addEventListener('unload', handleBeforeUnload)

  // Cleanup function
  return () => {
    if (inactivityTimer) {
      clearTimeout(inactivityTimer)
    }
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('beforeunload', handleBeforeUnload)
    window.removeEventListener('unload', handleBeforeUnload)
  }
}

// Hook for checking if we're on the client side
export const useIsClient = () => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return isClient
}
