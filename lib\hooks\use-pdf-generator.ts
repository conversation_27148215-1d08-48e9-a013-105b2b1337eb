import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { PDFGeneratorService, type PDFGenerationOptions } from '@/lib/services/pdf-generator'
import toast from 'react-hot-toast'

export interface UsePDFGeneratorReturn {
  generatePDF: (options: PDFGenerationOptions) => Promise<void>
  isGenerating: boolean
  error: Error | null
}

/**
 * Custom hook for PDF generation with loading states and error handling
 */
export function usePDFGenerator(): UsePDFGeneratorReturn {
  const [error, setError] = useState<Error | null>(null)

  const pdfMutation = useMutation({
    mutationFn: async (options: PDFGenerationOptions) => {
      const pdfService = new PDFGeneratorService()
      await pdfService.downloadTablePDF(options)
    },
    onMutate: () => {
      setError(null)
      toast.loading('Génération du PDF en cours...', { id: 'pdf-generation' })
    },
    onSuccess: () => {
      // Detect device type for appropriate success message
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)

      if (isIOS) {
        toast.success('PDF généré! Appuyez sur "Télécharger" dans le nouvel onglet pour sauvegarder.', {
          id: 'pdf-generation',
          duration: 6000
        })
      } else if (isMobile) {
        toast.success('PDF téléchargé! Vérifiez vos téléchargements ou utilisez le partage.', {
          id: 'pdf-generation',
          duration: 5000
        })
      } else {
        toast.success('PDF téléchargé avec succès!', { id: 'pdf-generation' })
      }
    },
    onError: (error: Error) => {
      console.error('PDF generation failed:', error)
      setError(error)
      toast.error('Échec de la génération du PDF', { id: 'pdf-generation' })
    }
  })

  return {
    generatePDF: pdfMutation.mutateAsync,
    isGenerating: pdfMutation.isPending,
    error
  }
}

/**
 * Hook for generating PDF blob without downloading (for preview or custom handling)
 */
export function usePDFBlobGenerator() {
  const [error, setError] = useState<Error | null>(null)

  const blobMutation = useMutation({
    mutationFn: async (options: PDFGenerationOptions): Promise<Blob> => {
      const pdfService = new PDFGeneratorService()
      return await pdfService.generateTablePDF(options)
    },
    onMutate: () => {
      setError(null)
    },
    onError: (error: Error) => {
      console.error('PDF blob generation failed:', error)
      setError(error)
    }
  })

  return {
    generateBlob: blobMutation.mutateAsync,
    isGenerating: blobMutation.isPending,
    error,
    data: blobMutation.data
  }
}
