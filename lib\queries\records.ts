import { ApiClient } from "@/lib/api-client"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import type { CreateRecordRequest, UpdateRecordRequest, SearchParams } from "@/lib/api-client-types"
import toast from "react-hot-toast"


// Table name mapping - supports both French and English names
const TABLE_NAMES: Record<string, string> = {
  // By ID
  "1": "kbir",    // الكوفر الكبير
  "2": "adel",    // الكوفر عادل
  "3": "khalit",  // الكوفر خليط
  "4": "kredi",   // الكوفر كريدي

  // French names
  "coffre-grand": "kbir",
  "coffre-adel": "adel",
  "coffre-mixte": "khalit",
  "coffre-credit": "kredi",

  // English names
  "big-safe": "kbir",
  "adel-safe": "adel",
  "mixed-safe": "khalit",
  "credit-safe": "kredi",

  // Direct API names (for backend compatibility)
  "kbir": "kbir",
  "adel": "adel",
  "khalit": "khalit",
  "kredi": "kredi"
}

// Arabic to API name mapping
const ARABIC_TO_API: Record<string, string> = {
  "الكوفر الكبير": "kbir",
  "الكوفر عادل": "adel",
  "الكوفر خليط": "khalit",
  "الكوفر كريدي": "kredi"
}

function getTableName(tableIdentifier: string | number): string {
  // Convert number to string for consistent lookup
  const identifier = String(tableIdentifier)

  // First try direct lookup in TABLE_NAMES
  if (TABLE_NAMES[identifier]) {
    return TABLE_NAMES[identifier]
  }

  // Try Arabic name lookup
  if (ARABIC_TO_API[identifier]) {
    return ARABIC_TO_API[identifier]
  }

  // Default fallback
  return "kbir"
}

// Query Keys
export const recordsKeys = {
  all: ["records"] as const,
  lists: () => [...recordsKeys.all, "list"] as const,
  list: (tableName: string, filters?: Record<string, any>) => 
    [...recordsKeys.lists(), tableName, { filters }] as const,
  searches: () => [...recordsKeys.all, "search"] as const,
  search: (tableName: string, params: SearchParams) => 
    [...recordsKeys.searches(), tableName, params] as const,
}

export function mapTableIdWithName(tableId: string | number): string {
  if(tableId == 1) return "adils"
  if(tableId == 2) return "credits"
  if(tableId == 3) return "khalits"
  if(tableId == 4) return "lkbirs"
  if(tableId == 5) return "adils-2"
  if(tableId == 6) return "adils-3"
  if(tableId == 7) return "adils-4"
  if(tableId == 8) return "adils-5"
  if(tableId == 9) return "adils-6"
  if(tableId == 10) return "adils-7"
  if(tableId == 11) return "adils-8"
  if(tableId == 12) return "adils-9"
  if(tableId == 13) return "adils-10"
  if(tableId == 14) return "adils-11"
  if(tableId == 15) return "adils-12"
  if(tableId == 16) return "adils-13"
  if(tableId == 17) return "adils-14"
  if(tableId == 18) return "adils-15"
  if(tableId == 19) return "adils-16"
  if(tableId == 20) return "adils-17"
  return "aa"
}

// Main hook for fetching records of a specific table (like useCoffres for tables)
export function useTableRecords(tableId: string | number, params?: { page?: number; size?: number }) {
  const tableName = getTableName(tableId)

  return useQuery({
    queryKey: recordsKeys.list(tableName, params),
    queryFn: async () => {
      try {
        console.log(`🔄 Fetching records for table ${tableId} (${tableName})`, params)
        const result = await ApiClient.getRecords(mapTableIdWithName(tableId), params)
        console.log(`✅ Fetched ${result.elements?.length || 0} records for table ${tableId}`)
        return result
      } catch (error) {
        console.error(`❌ Failed to fetch records for table ${tableId}:`, error)

        // Check if it's an authentication error
        if (error instanceof Error) {
          if (error.message.includes('403') || error.message.includes('Forbidden')) {
            console.log('🔄 403 error detected - ApiClient will handle token refresh automatically')
          } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
            console.log('🚫 401 error detected - user will be redirected to login')
          } else if (error.message.includes('Session expired') || error.message.includes('login again')) {
            console.log('🚫 Session expired - user will be redirected to login')
          }
        }

        throw error
      }
    },
    enabled: !!tableId,
    staleTime: 0, // Always fetch fresh data when navigating to details
    gcTime: 2 * 60 * 1000, // Keep in cache for 2 minutes after component unmounts
    refetchOnMount: false, // Don't auto-refetch on mount to avoid duplicate calls
    refetchOnWindowFocus: false, // Don't refetch on window focus to avoid unnecessary calls
    retry: (failureCount, error) => {
      // Don't retry on authentication errors to avoid infinite loops
      if (error instanceof Error) {
        const errorMessage = error.message.toLowerCase()
        if (errorMessage.includes('401') ||
            errorMessage.includes('403') ||
            errorMessage.includes('unauthorized') ||
            errorMessage.includes('session expired') ||
            errorMessage.includes('login again')) {
          console.log('🚫 Authentication error - not retrying')
          return false
        }
      }
      // Retry other errors up to 2 times
      return failureCount < 2
    },
  })
}

// Legacy hook for backward compatibility
export function useRecords(tableIdentifier: string | number, params?: { page?: number; size?: number }) {
  return useTableRecords(tableIdentifier, params)
}

// Hook to fetch only the rest amount for a specific table (lightweight)
export function useTableRestAmount(tableId: string | number) {
  const tableName = getTableName(tableId)

  return useQuery({
    queryKey: [...recordsKeys.list(tableName), 'rest'],
    queryFn: async () => {
      try {
        const result = await ApiClient.getRecords(mapTableIdWithName(tableId), { page: 0, size: 1 })
        return {
          tableId: tableId,
          rest: result.rest || 0,
          coffreTotal: result.coffreTotal || 0
        }
      } catch (error) {
        console.error(`Failed to fetch rest amount for table ${tableId}:`, error)
        return {
          tableId: tableId,
          rest: 0,
          coffreTotal: 0
        }
      }
    },
    enabled: !!tableId,
    staleTime: 1 * 60 * 1000, // 1 minute (shorter since this is for home page)
    gcTime: 3 * 60 * 1000, // 3 minutes
  })
}

// Hook to fetch rest amounts for multiple tables (for home page)
export function useTablesRestAmounts(tableIds: (string | number)[]) {
  return useQuery({
    queryKey: ['tables-rest-amounts', tableIds],
    queryFn: async () => {
      try {
        console.log(`🏠 [useTablesRestAmounts] Fetching fresh rest amounts for ${tableIds.length} tables`)

        // Fetch rest amounts for all tables in parallel
        const promises = tableIds.map(async (tableId) => {
          try {
            console.log(`🔄 [useTablesRestAmounts] Fetching rest for table ${tableId}`)
            // Call table details API - backend already calculates the rest value
            const result = await ApiClient.getRecords(mapTableIdWithName(tableId), { page: 0, size: 20 })
            console.log(`✅ [useTablesRestAmounts] Got rest ${result.rest} for table ${tableId}`)
            return {
              tableId: tableId,
              rest: result.rest, // Direct value from backend - no calculation needed
            }
          } catch (error) {
            console.error(`❌ [useTablesRestAmounts] Failed to fetch rest amount for table ${tableId}:`, error)
            return {
              tableId: tableId,
              rest: 0,
            }
          }
        })

        const results = await Promise.all(promises)

        // Convert to a simple map for easy lookup: tableId -> rest amount
        const restAmountsMap = new Map()
        results.forEach(result => {
          restAmountsMap.set(result.tableId.toString(), result.rest)
        })

        console.log(`✅ [useTablesRestAmounts] Completed fetching rest amounts for all tables`)
        return restAmountsMap
      } catch (error) {
        console.error('❌ [useTablesRestAmounts] Failed to fetch rest amounts for tables:', error)
        return new Map()
      }
    },
    enabled: tableIds.length > 0,
    staleTime: 0, // ALWAYS fetch fresh data when cache is cleared
    gcTime: 1 * 60 * 1000, // Keep in cache for only 1 minute after component unmounts
    refetchOnMount: false, // Don't auto-refetch - cache clearing will trigger fetch
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
  })
}

// Mutation hook for creating record
export function useCreateRecord(tableId: string) {
  return useMutation({
    mutationFn: (data: CreateRecordRequest) => ApiClient.createRecord(mapTableIdWithName(tableId), data),
    onSuccess: () => {
      toast.success('Enregistrement ajouté avec succès!')
    },
  })
}

// Mutation hook for updating record
export function useUpdateRecord(tableId: string) {
  const tableName = getTableName(tableId)

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateRecordRequest }) => {
      // Inject record ID into the request data
      const dataWithId = {
        ...data,
        id: id // Add record ID to the request body (same as path variable)
      }

      console.log(`✏️ Updating record ${id} in table ${tableId} (${tableName})`, dataWithId)
      return ApiClient.updateRecord(mapTableIdWithName(tableId), id, dataWithId)
    },
    onSuccess: (data, variables) => {
      toast.success('Enregistrement mis à jour avec succès!')
    },
    onError: (error) => {
      toast.error('Échec de la mise à jour de l\'enregistrement')
    },
  })
}

// Mutation hook for deleting record
export function useDeleteRecord(tableId: string) {
  const queryClient = useQueryClient()
  const tableName = getTableName(tableId)

  return useMutation({
    mutationFn: (id: number) => {
      return ApiClient.deleteRecord(mapTableIdWithName(tableId), id)
    },
    onSuccess: (_, recordId) => {
      toast.success('Enregistrement supprimé avec succès!')
    },
    onError: (error) => {
      toast.error('Échec de la suppression de l\'enregistrement')
    },
  })
}
