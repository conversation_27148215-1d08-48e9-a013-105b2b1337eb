"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, TrendingUp, Eye, EyeOff } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useState } from "react"

interface RecordDetailProps {
  record: {
    id: string
    name: string
    amount: number
    dateOut: string | null
    dateReturn: string | null
    totalRecords: number
    period: string
    totalAmount: number
  }
  onClose?: () => void
}

export function RecordDetailCard({ record, onClose }: RecordDetailProps) {
  const [isAmountHidden, setIsAmountHidden] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 2
    }).format(amount).replace('€', '€')
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return null
    const date = new Date(dateString)
    return date.toLocaleDateString('fr-FR')
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md bg-white rounded-2xl shadow-xl">
        <CardContent className="p-0">
          {/* Header with search and menu */}
          <div className="flex items-center justify-between p-4 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-gray-500 text-sm">🔍</span>
              </div>
              <span className="text-gray-400 text-sm">بحث...</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="bg-blue-500 text-white rounded-lg px-3 py-1"
              onClick={onClose}
            >
              ☰
            </Button>
          </div>

          {/* Statistics Section */}
          <div className="p-4 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-600 mb-3">إحصائيات</h3>
            
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-xs text-gray-500">إجمالي السجلات</div>
                <div className="text-lg font-bold text-gray-900">{record.totalRecords}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-500">الفترة</div>
                <div className="text-lg font-bold text-gray-900">{record.period}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-500">المبلغ الإجمالي</div>
                <div className="text-lg font-bold text-gray-900">
                  {isAmountHidden ? "••••••" : formatCurrency(record.totalAmount)}
                </div>
              </div>
            </div>
          </div>

          {/* Record Details */}
          <div className="p-4">
            {/* Date Badge */}
            <div className="flex items-center justify-between mb-4">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <Calendar className="w-3 h-3 mr-1" />
                {formatDate(record.dateOut) || "تاريخ غير محدد"}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsAmountHidden(!isAmountHidden)}
                className="p-1"
              >
                {isAmountHidden ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            </div>

            {/* Amount */}
            <div className="text-right mb-4">
              <div className="text-2xl font-bold text-blue-600">
                {isAmountHidden ? "••••••" : formatCurrency(record.amount)}
              </div>
            </div>

            {/* Name */}
            <div className="text-right mb-6">
              <h2 className="text-xl font-bold text-gray-900">{record.name}</h2>
            </div>

            {/* Dates Section */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">تاريخ الخروج</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span className="text-sm font-medium">
                    {formatDate(record.dateOut) || "غير محدد"}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">تاريخ الإرجاع</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                  <span className="text-sm font-medium">
                    {formatDate(record.dateReturn) || "غير محدد"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
