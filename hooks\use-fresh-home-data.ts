import { useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useCoffres } from '@/lib/queries/tables'
import { useTablesRestAmounts } from '@/lib/queries/records'

/**
 * Custom hook that ensures fresh data is always fetched when on the home page
 * This hook clears cache ONCE and lets the hooks handle the fetching
 */
export function useFreshHomeData() {
  const queryClient = useQueryClient()

  // Clear cache ONCE when home page mounts to ensure fresh data
  useEffect(() => {
    console.log('🏠 [useFreshHomeData] Home page mounted - clearing cache for fresh data')

    // Clear all tables and records cache ONCE
    queryClient.removeQueries({
      queryKey: ['tables'],
      exact: false
    })

    queryClient.removeQueries({
      queryKey: ['tables-rest-amounts'],
      exact: false
    })

    console.log('✅ [useFreshHomeData] Cache cleared - hooks will fetch fresh data')

  }, [queryClient])
  
  // Fetch tables data (will be fresh due to cache clearing)
  const tablesQuery = useCoffres()
  
  // Get table IDs for rest amounts
  const coffres = (tablesQuery.data || []).sort((a, b) => a.id - b.id)
  const tableIds = coffres.map(coffre => coffre.id)
  
  // Fetch rest amounts (will be fresh due to cache clearing)
  const restAmountsQuery = useTablesRestAmounts(tableIds)
  
  return {
    // Tables data
    coffres,
    isLoadingTables: tablesQuery.isLoading,
    tablesError: tablesQuery.error,
    refetchTables: tablesQuery.refetch,
    
    // Rest amounts data
    restAmountsMap: restAmountsQuery.data,
    isLoadingRestAmounts: restAmountsQuery.isLoading,
    restAmountsError: restAmountsQuery.error,
    refetchRestAmounts: restAmountsQuery.refetch,
    
    // Combined loading state
    isLoading: tablesQuery.isLoading || restAmountsQuery.isLoading,
    
    // Combined error state
    error: tablesQuery.error || restAmountsQuery.error,
    
    // Combined refetch function
    refetchAll: async () => {
      console.log('🔄 [useFreshHomeData] Manual refetch - clearing cache and fetching fresh data')
      
      // Clear cache first
      queryClient.removeQueries({ 
        queryKey: ['tables'], 
        exact: false 
      })
      
      queryClient.removeQueries({ 
        queryKey: ['tables-rest-amounts'], 
        exact: false 
      })
      
      // Then refetch
      await Promise.all([
        tablesQuery.refetch(),
        restAmountsQuery.refetch()
      ])
      
      console.log('✅ [useFreshHomeData] Manual refetch completed')
    }
  }
}
