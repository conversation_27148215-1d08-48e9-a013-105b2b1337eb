import { useMutation, useQueryClient } from "@tanstack/react-query"
import { apiClient } from "../client"
import { mockDelay } from "../mock-data"
import { tokenStorage } from "@/lib/utils/storage"
import type { LoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse, ChangePasswordRequest } from "../types"

const IS_MOCK_MODE = process.env.NEXT_PUBLIC_USE_MOCK_API === "true"

// Query Keys
export const authKeys = {
  all: ["auth"] as const,
  user: () => [...authKeys.all, "user"] as const,
  profile: (id: string) => [...authKeys.user(), id] as const,
}

// Auth Service
export class AuthService {
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    console.log("AuthService.login called with JWT token support")

    if (IS_MOCK_MODE) {
      console.log("🔧 Mock login with credentials:", credentials)
      await mockDelay(500) // Small delay to simulate authentication

      const mockResponse: LoginResponse = {
        accessToken: "mock-access-token-" + Date.now(),
        refreshToken: "mock-refresh-token-" + Date.now(),
      }

      console.log("🔧 Mock login successful with JWT tokens:", mockResponse)
      return mockResponse
    }

    // Real API call
    const response = await apiClient.post<LoginResponse>("/auth/login", credentials)
    return response
  }

  static async logout(): Promise<void> {
    console.log("AuthService.logout called")

    if (IS_MOCK_MODE) {
      console.log("🔧 Mock logout")
      await mockDelay(200)
      return
    }

    // Real API call - send current refresh token for server-side cleanup
    const refreshToken = tokenStorage.getRefreshToken()
    if (refreshToken) {
      try {
        await apiClient.post("/auth/logout", { refreshToken })
      } catch (error) {
        // Log the error but don't throw it - we want to clear local data regardless
        console.error("Server logout failed:", error)
      }
    }
  }

  // No getCurrentUser method needed - user data comes from login response

  static async changePassword(request: ChangePasswordRequest): Promise<void> {
    console.log("AuthService.changePassword called")

    if (IS_MOCK_MODE) {
      console.log("🔧 Mock change password")
      await mockDelay(500)

      // Simulate password validation
      if (request.oldPassword === "wrongpassword") {
        throw new Error("Current password is incorrect")
      }

      console.log("🔧 Mock password change successful")
      return
    }

    // Real API call
    await apiClient.post("/auth/change-password", request)
  }

  static async refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    console.log("AuthService.refreshToken called")

    if (IS_MOCK_MODE) {
      console.log("🔧 Mock refresh token")
      await mockDelay(300)

      return {
        accessToken: "new-mock-access-token-" + Date.now(),
        refreshToken: "new-mock-refresh-token-" + Date.now(),
      }
    }

    // Real API call
    const response = await apiClient.post<RefreshTokenResponse>("/auth/refresh", request)
    return response
  }
}

// React Query Hooks
export function useLogin() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ username, password }: { username: string; password: string }) => {
      // Store username for creating fake user
      return AuthService.login({ username, password }).then(response => ({
        ...response,
        username // Pass username along with response
      }))
    },
    onSuccess: (data) => {
      console.log("Login mutation success with JWT tokens:", data)

      // Store JWT tokens in localStorage
      apiClient.setTokens(data.accessToken, data.refreshToken)

      // Create fake user from username
      const fakeUser = {
        id: "1",
        name: data.username,
        email: `${data.username}@example.com`,
        role: "user",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Cache the fake user data
      queryClient.setQueryData(authKeys.user(), fakeUser)

      console.log("JWT tokens stored and fake user created:", fakeUser)
    },
    onError: (error) => {
      console.error("Login failed:", error)
      // Clear any cached auth data on error
      queryClient.removeQueries({ queryKey: authKeys.all })
      apiClient.clearAllTokens()
    },
  })
}

export function useLogout() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: AuthService.logout,
    onMutate: async () => {
      console.log("Logout mutation starting - clearing JWT tokens and cache")
      // Clear JWT tokens immediately
      apiClient.clearAllTokens()
      // Clear the cache
      await queryClient.cancelQueries({ queryKey: authKeys.all })
      queryClient.clear()
    },
    onSuccess: () => {
      console.log("Logout successful")
      // Clear JWT tokens and cached data
      apiClient.clearAllTokens()
      queryClient.clear()
    },
    onError: (error) => {
      console.error("Logout failed:", error)
      // Even if logout fails, clear local data
      apiClient.clearAllTokens()
      queryClient.clear()
    },
    onSettled: () => {
      // Ensure tokens and cache are cleared regardless of success/failure
      apiClient.clearAllTokens()
      queryClient.clear()
      console.log("Logout completed - JWT tokens and data cleared")
    },
  })
}
export function useRefreshToken() {
  return useMutation({
    mutationFn: (refreshToken: string) => AuthService.refreshToken({ refreshToken }),
    onSuccess: (data) => {
      console.log("Token refresh successful")
      // Update stored tokens
      apiClient.setTokens(data.accessToken, data.refreshToken)
    },
    onError: (error) => {
      console.error("Token refresh failed:", error)
      // If refresh fails, clear everything
      apiClient.clearAllTokens()
    },
  })
}

export function useChangePassword() {
  return useMutation({
    mutationFn: (request: ChangePasswordRequest) => AuthService.changePassword(request),
    onSuccess: () => {
      console.log("Password change successful")
    },
    onError: (error) => {
      console.error("Password change failed:", error)
    },
  })
}
