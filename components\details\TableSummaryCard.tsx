"use client"

import { <PERSON>, <PERSON>O<PERSON>, <PERSON> } from "lucide-react"
import { Card, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/contexts/language-context"

interface TableSummaryCardProps {
  currentTableName: string
  currentTableColor: string
  container: any
  isPriceHidden: boolean
  isTableNameHidden: boolean
  totalUsed: number
  recordsCount: number
  showStickySummary: boolean
  onTogglePriceVisibility: () => void
  onToggleNameVisibility: () => void
  formatDate: (date: string) => string
  formatCurrencyFrench: (amount: number) => string
}

export function TableSummaryCard({
  currentTableName,
  currentTableColor,
  container,
  isPriceHidden,
  isTableNameHidden,
  totalUsed,
  recordsCount,
  showStickySummary,
  onTogglePriceVisibility,
  onToggleNameVisibility,
  formatDate,
  formatCurrencyFrench
}: TableSummaryCardProps) {
  const { t, isRTL } = useLanguage()

  return (
    <Card className={`mb-8 overflow-hidden border-0 shadow-lg ${showStickySummary ? 'mt-24 md:mt-16' : ''}`}>
      <CardHeader className={`${currentTableColor} ${container?.textColor || 'text-black'} p-6`}>
        <div className="flex items-center justify-end flex-row-reverse space-x-reverse">
          <Button
            variant="ghost"
            size="sm"
            onClick={onTogglePriceVisibility}
            className="p-2 h-8 w-8 text-white hover:bg-white/20"
          >
            {isPriceHidden ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
        </div>

        <div className="space-y-6">
          {/* Mobile Layout */}
          <div className="block md:hidden">
            {/* Title and Last Update Row */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex-1 mr-4">
                <h3 className="font-bold text-gray-900 text-base truncate text-right" dir="rtl">
                  {currentTableName.startsWith("home.") ? t(currentTableName) : currentTableName}
                </h3>
              </div>

              <div className="flex items-center space-x-2">
                {/* Price Toggle Eye Icon - Mobile */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onTogglePriceVisibility}
                  className="p-1 h-6 w-6 text-white hover:bg-white/20"
                >
                  {isPriceHidden ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                </Button>
              </div>
            </div>

            {/* Last Update - Mobile */}
            <div className="flex justify-center mb-4">
              <div className="bg-red-600 rounded-lg px-3 py-2 border border-red-500 shadow-lg">
                <div className="flex items-center justify-center space-x-2 space-x-reverse" dir="rtl">
                  <span className="font-bold text-white text-sm date-french bg-black/20 rounded px-2 py-0.5">{formatDate(container.lastUpdated)}</span>
                  <span className="text-white/90 font-medium text-xs">آخر تحديث</span>
                  <Clock className="h-3 w-3 text-red-200 animate-pulse" />
                </div>
              </div>
            </div>

            {/* Financial Data Grid */}
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="bg-blue-50 rounded-lg p-2 text-center border border-blue-200">
                <div className="text-blue-700 font-medium text-xs mb-1" dir="rtl">الأولي</div>
                <div className="font-bold text-blue-800 number-french">
                  {isPriceHidden ? "••••" : formatCurrencyFrench(container.initial)}
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-2 text-center border border-green-200">
                <div className="text-green-700 font-medium text-xs mb-1" dir="rtl">الحالي</div>
                <div className="font-bold text-green-800 number-french">
                  {isPriceHidden ? "••••" : formatCurrencyFrench(container.actual)}
                </div>
              </div>

              <div className="bg-red-50 rounded-lg p-2 text-center border border-red-200">
                <div className="text-red-700 font-medium text-xs mb-1" dir="rtl">المستهلك</div>
                <div className="font-bold text-red-800 number-french">
                  {isPriceHidden ? "••••" : formatCurrencyFrench(totalUsed)}
                </div>
              </div>

              <div className="bg-yellow-50 rounded-lg p-2 text-center border border-yellow-200">
                <div className="text-yellow-700 font-medium text-xs mb-1" dir="rtl">السجلات</div>
                <div className="font-bold text-yellow-800 number-french">{recordsCount}</div>
              </div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:block">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <Clock className="h-4 w-4 text-white/80" />
                  <span className="text-white/90 text-sm">{t("details.lastUpdate")}</span>
                  <span className="font-bold text-white text-sm">{formatDate(container.lastUpdated)}</span>
                </div>
              </div>

              <div className="flex items-center space-x-6">
                <div className="text-center">
                  <h3
                    className="font-semibold text-gray-900 truncate flex-1 cursor-pointer select-none hover:scale-105 transition-transform duration-200"
                    style={{direction: 'rtl', textAlign: 'right', unicodeBidi: 'embed'}}
                    onClick={onToggleNameVisibility}
                  >
                    {isTableNameHidden ? "********" : (currentTableName.startsWith("home.") ? t(currentTableName) : currentTableName)}
                  </h3>
                </div>

                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <span className="text-white/90">{t("details.records")}</span>
                    <span className="font-bold text-white">{recordsCount}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-white/80 text-sm mb-1">{t("details.initial")}</div>
                <div className="text-2xl font-bold text-white">
                  {isPriceHidden ? "••••••" : formatCurrencyFrench(container.initial)}
                </div>
              </div>
              <div>
                <div className="text-white/80 text-sm mb-1">{t("details.current")}</div>
                <div className="text-2xl font-bold text-white">
                  {isPriceHidden ? "••••••" : formatCurrencyFrench(container.actual)}
                </div>
              </div>
              <div>
                <div className="text-white/80 text-sm mb-1">{t("details.used")}</div>
                <div className="text-2xl font-bold text-white">
                  {isPriceHidden ? "••••••" : formatCurrencyFrench(totalUsed)}
                </div>
              </div>
            </div>
          </div>

          {/* Sticky Header Content */}
          <div className="hidden md:block">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <h2
                      className="text-2xl font-bold mb-1 transition-all duration-300 text-right cursor-pointer select-none"
                      dir="rtl"
                      style={{textAlign: 'right'}}
                      onClick={onToggleNameVisibility}
                    >
                      {isTableNameHidden ? "********" : (currentTableName.startsWith("home.") ? t(currentTableName) : currentTableName)}
                    </h2>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
    </Card>
  )
}
