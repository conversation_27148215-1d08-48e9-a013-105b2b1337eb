"use client"

import { Languages } from "lucide-react"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useLanguage, type Language } from "@/contexts/language-context"
import { useDirection } from "@radix-ui/react-direction"
import { useEnhancedAnalytics } from "@/hooks/useAnalytics"

export function LanguageSwitcher() {
  const { language, setLanguage, t } = useLanguage()
  const dir = useDirection()
  const analytics = useEnhancedAnalytics()

  const languages: { code: Language; name: string; nativeName: string }[] = [
    { code: "en", name: "English", nativeName: "English" },
    { code: "fr", name: "French", nativeName: "Français" },
    { code: "ar", name: "Arabic", nativeName: "العربية" },
  ]

  return (
    <DropdownMenu onOpenChange={(open) => {
      if (open) {
        analytics.trackMenuOpen('language_switcher', 'header')
      }
    }}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="hover:bg-[#E6F0FA]">
          <Languages className="h-5 w-5 text-gray-600" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={dir === "rtl" ? "start" : "end"} className="w-48">
        {languages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onClick={() => {
              const previousLanguage = language
              analytics.trackLanguageChange(previousLanguage, lang.code)
              setLanguage(lang.code)
            }}
            className={`cursor-pointer ${language === lang.code ? "bg-[#E6F0FA]" : ""}`}
          >
            <div className="flex items-center justify-between w-full">
              <span>{lang.nativeName}</span>
              {language === lang.code && <div className="w-2 h-2 bg-[#0A66C2] rounded-full" />}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
