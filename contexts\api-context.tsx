"use client"

import { useState } from "react"
import type { ReactNode } from "react"
import { createContext, useContext, useEffect } from "react"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { apiClient } from "@/lib/api/client"

interface ApiContextType {
  queryClient: QueryClient
  apiClient: typeof apiClient
  isOnline: boolean
}

const ApiContext = createContext<ApiContextType | undefined>(undefined)

// Create a stable query client instance
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors except 408, 429
        if (error?.code >= 400 && error?.code < 500 && ![408, 429].includes(error?.code)) {
          return false
        }
        return failureCount < 3
      },
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: (failureCount, error: any) => {
        // Don't retry mutations on client errors
        if (error?.code >= 400 && error?.code < 500) {
          return false
        }
        return failureCount < 2
      },
    },
  },
})

// Online status hook
function useOnlineStatus() {
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    // Only set up online/offline listeners on client side
    if (typeof navigator === "undefined") return

    setIsOnline(navigator.onLine)

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  return isOnline
}

// API Provider Component
export function ApiProvider({ children }: { children: ReactNode }) {
  const isOnline = useOnlineStatus()

  // Global error handling is now managed by the API client automatically

  // JWT tokens are automatically loaded by the API client from localStorage
  // No need to manually initialize tokens here

  const contextValue: ApiContextType = {
    queryClient,
    apiClient,
    isOnline,
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ApiContext.Provider value={contextValue}>
        {children}
        {typeof window !== "undefined" && process.env.NODE_ENV === "development" && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </ApiContext.Provider>
    </QueryClientProvider>
  )
}