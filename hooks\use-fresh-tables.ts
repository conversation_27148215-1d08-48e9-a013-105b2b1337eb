import { useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useCoffres } from '@/lib/queries/tables'

/**
 * Custom hook that ensures fresh tables data is always fetched when on the home page
 * This hook clears ONLY tables cache and fetches fresh tables list - NO individual table details
 */
export function useFreshTables() {
  const queryClient = useQueryClient()
  
  // Clear tables cache ONCE when home page mounts to ensure fresh data
  useEffect(() => {
    console.log('🏠 [useFreshTables] Home page mounted - clearing tables cache for fresh data')
    
    // Clear ONLY tables cache - no records or rest amounts
    queryClient.removeQueries({ 
      queryKey: ['tables'], 
      exact: false 
    })
    
    console.log('✅ [useFreshTables] Tables cache cleared - will fetch fresh tables list only')
    
  }, [queryClient])
  
  // Fetch only tables data (will be fresh due to cache clearing)
  const tablesQuery = useCoffres()
  
  // Sort tables by ID from small to big (1, 2, 3, 4...)
  const coffres = (tablesQuery.data || []).sort((a, b) => a.id - b.id)
  
  return {
    // Tables data
    coffres,
    isLoading: tablesQuery.isLoading,
    error: tablesQuery.error,
    refetch: tablesQuery.refetch,
  }
}
