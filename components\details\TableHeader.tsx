"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye, EyeOff } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/contexts/language-context"

interface TableHeaderProps {
  currentTableName: string
  isTableNameHidden: boolean
  isPriceHidden: boolean
  onToggleNameVisibility: () => void
  onTogglePriceVisibility: () => void
  onEditTable: () => void
}

export function TableHeader({
  currentTableName,
  isTableNameHidden,
  isPriceHidden,
  onToggleNameVisibility,
  onTogglePriceVisibility,
  onEditTable
}: TableHeaderProps) {
  const { t, isRTL } = useLanguage()

  return (
    <div className="flex items-center justify-between mb-6">
      <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
        <ArrowLeft className="h-5 w-5" />
        <span className="font-medium">{t("navigation.back")}</span>
      </Link>
      <div className="w-full text-right" dir="rtl">
        <div className="flex items-center justify-end space-x-2">
          <div className="text-right">
            <h1 className="text-xl font-semibold text-gray-900 text-right" dir="rtl">
              {isTableNameHidden ? "••••••••" : (currentTableName.startsWith("home.") ? t(currentTableName) : currentTableName)}
            </h1>
          </div>
        </div>
      </div>
      
      {/* Control Icons */}
      <div className={`flex items-center space-x-2 ${isRTL ? "space-x-reverse" : ""}`}>
        {/* Name visibility toggle */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleNameVisibility}
          className="p-2 h-8 w-8"
          title={isTableNameHidden ? "Show table name" : "Hide table name"}
        >
          {isTableNameHidden ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        </Button>
        
        {/* Price visibility toggle */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onTogglePriceVisibility}
          className="p-2 h-8 w-8"
          title={isPriceHidden ? "Show prices" : "Hide prices"}
        >
          {isPriceHidden ? <EyeOff className="h-4 w-4 text-blue-600" /> : <Eye className="h-4 w-4 text-blue-600" />}
        </Button>
        
        {/* Edit table button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onEditTable}
          className="p-2 h-8 w-8"
          title="Edit table"
        >
          <Edit className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
