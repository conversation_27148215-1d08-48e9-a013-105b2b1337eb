"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Eye, EyeOff, Lock, Mail, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/contexts/auth-context"
import { useLanguage } from "@/contexts/language-context"
import { useRouter } from "next/navigation"
import { sendGAEvent } from "@next/third-parties/google"

export default function SignInPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const { signIn, isLoading, error, clearError, isAuthenticated } = useAuth()
  const { t } = useLanguage()
  const router = useRouter()

  useEffect(() => {
    if (isAuthenticated) {
      console.log("User is authenticated, redirecting to home...")
      setIsRedirecting(true)
      // Use a small delay to ensure state is stable
      setTimeout(() => {
        router.replace("/")
      }, 100)
    }
  }, [isAuthenticated, router])

  const handleButtonClick = () => {
    sendGAEvent('event', 'buttonClicked', { value: 'xyz' })
  };

  // Clear error when component mounts or inputs change
  useEffect(() => {
    clearError()
  }, [email, password, clearError])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()
    handleButtonClick()

    if (!email.trim() || !password.trim()) {
      return
    }

    try {
      sendGAEvent('event', 'buttonClicked', { value: 'xyz' })
      await signIn(email, password)
    } catch (error) {
      console.error("Sign in failed:", error)
    }
  }

  // Show loading screen if redirecting or already authenticated
  if (isAuthenticated || isRedirecting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#0A66C2] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Redirection vers le tableau de bord...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-2xl">
        <CardHeader className="text-center pb-6">
          <div className="w-10 h-10 text-center flex items-center justify-center">
              <img
                src="/app_logo.png"
                alt="App Logo"
                className="w-full h-full object-contain"
              />
            </div>
          <CardTitle className="text-2xl font-bold text-gray-900">Les Coffres d'Adil</CardTitle>
          <p className="text-gray-600">Connectez-vous à votre compte</p>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span>E-mail</span>
              </Label>
              <Input
                id="email"
                type="text"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Entrez votre e-mail"
                required
                disabled={isLoading}
                className="transition-colors"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="flex items-center space-x-2">
                <Lock className="h-4 w-4" />
                <span>Mot de passe</span>
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Entrez votre mot de passe"
                  required
                  disabled={isLoading}
                  className="transition-colors pr-10 [&::-ms-reveal]:hidden [&::-webkit-credentials-auto-fill-button]:hidden"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </Button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-[#0A66C2] hover:bg-[#0A66C2]/90 transition-colors"
              disabled={isLoading || !email.trim() || !password.trim()}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Connexion en cours...</span>
                </div>
              ) : (
                "Se connecter"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
